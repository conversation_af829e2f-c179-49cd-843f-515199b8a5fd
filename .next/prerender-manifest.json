{"version": 4, "routes": {"/robots.txt": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "text/plain", "x-next-cache-tags": "_N_T_/layout,_N_T_/robots.txt/layout,_N_T_/robots.txt/route,_N_T_/robots.txt"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/robots.txt", "dataRoute": null}, "/sitemap.xml": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "application/xml", "x-next-cache-tags": "_N_T_/layout,_N_T_/sitemap.xml/layout,_N_T_/sitemap.xml/route,_N_T_/sitemap.xml"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/sitemap.xml", "dataRoute": null}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "ebe81a3b1258eb7d7d180b16d4528337", "previewModeSigningKey": "5b4960c053a4ef52338a9bef6b6ddfa7c48db10e18f1421bc6899e222b612772", "previewModeEncryptionKey": "eb956e575a3c7f29e2e99b9d1d51fe6aba4eda5c4669f39bf8ee331432349ee8"}}