/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/interactive-tools/page";
exports.ids = ["app/[locale]/interactive-tools/page"];
exports.modules = {

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	],
	"./zh.json": [
		"(rsc)/./messages/zh.json",
		"_rsc_messages_zh_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "next/dist/compiled/@vercel/og/index.node.js":
/*!**************************************************************!*\
  !*** external "next/dist/compiled/@vercel/og/index.node.js" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("next/dist/compiled/@vercel/og/index.node.js");;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Finteractive-tools%2Fpage&page=%2F%5Blocale%5D%2Finteractive-tools%2Fpage&appPaths=%2F%5Blocale%5D%2Finteractive-tools%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Finteractive-tools%2Fpage.tsx&appDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Finteractive-tools%2Fpage&page=%2F%5Blocale%5D%2Finteractive-tools%2Fpage&appPaths=%2F%5Blocale%5D%2Finteractive-tools%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Finteractive-tools%2Fpage.tsx&appDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'interactive-tools',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/interactive-tools/page.tsx */ \"(rsc)/./app/[locale]/interactive-tools/page.tsx\")), \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/layout.tsx */ \"(rsc)/./app/[locale]/layout.tsx\")), \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/icon.tsx?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/icon.tsx?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/icon.tsx?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/icon.tsx?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/interactive-tools/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/interactive-tools/page\",\n        pathname: \"/[locale]/interactive-tools\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Finteractive-tools%2Fpage&page=%2F%5Blocale%5D%2Finteractive-tools%2Fpage&appPaths=%2F%5Blocale%5D%2Finteractive-tools%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Finteractive-tools%2Fpage.tsx&appDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2FBreathingExercise.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2FBreathingExercise.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/BreathingExercise.tsx */ \"(ssr)/./components/BreathingExercise.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZHV0aW5nJTJGRG93bmxvYWRzJTJGcGVyaW9kaHViLWhlYWx0aF8lRTUlODklQUYlRTYlOUMlQUMwMSVFNyU4OSU4OCUyRmNvbXBvbmVudHMlMkZCcmVhdGhpbmdFeGVyY2lzZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZHV0aW5nJTJGRG93bmxvYWRzJTJGcGVyaW9kaHViLWhlYWx0aF8lRTUlODklQUYlRTYlOUMlQUMwMSVFNyU4OSU4OCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdMQUFpSjtBQUNqSjtBQUNBLGdNQUF5SCIsInNvdXJjZXMiOlsid2VicGFjazovL3BlcmlvZGh1Yi1oZWFsdGgvPzBhNzYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL2R1dGluZy9Eb3dubG9hZHMvcGVyaW9kaHViLWhlYWx0aF/lia/mnKwwMeeJiC9jb21wb25lbnRzL0JyZWF0aGluZ0V4ZXJjaXNlLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2R1dGluZy9Eb3dubG9hZHMvcGVyaW9kaHViLWhlYWx0aF/lia/mnKwwMeeJiC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9saW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2FBreathingExercise.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Flink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Footer.tsx */ \"(ssr)/./components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Header.tsx */ \"(ssr)/./components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2Fadvanced%2FAppProvider.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2Fadvanced%2FAppProvider.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/advanced/AppProvider.tsx */ \"(ssr)/./components/advanced/AppProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZHV0aW5nJTJGRG93bmxvYWRzJTJGcGVyaW9kaHViLWhlYWx0aF8lRTUlODklQUYlRTYlOUMlQUMwMSVFNyU4OSU4OCUyRmNvbXBvbmVudHMlMkZhZHZhbmNlZCUyRkFwcFByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkFwcFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZHV0aW5nJTJGRG93bmxvYWRzJTJGcGVyaW9kaHViLWhlYWx0aF8lRTUlODklQUYlRTYlOUMlQUMwMSVFNyU4OSU4OCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJkaXNwbGF5JTVDJTIyJTNBJTVDJTIyc3dhcCU1QyUyMiUyQyU1QyUyMnZhcmlhYmxlJTVDJTIyJTNBJTVDJTIyLS1mb250LWludGVyJTVDJTIyJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZkdXRpbmclMkZEb3dubG9hZHMlMkZwZXJpb2RodWItaGVhbHRoXyVFNSU4OSVBRiVFNiU5QyVBQzAxJUU3JTg5JTg4JTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUF3SiIsInNvdXJjZXMiOlsid2VicGFjazovL3BlcmlvZGh1Yi1oZWFsdGgvPzE1ZjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBcHBQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9kdXRpbmcvRG93bmxvYWRzL3BlcmlvZGh1Yi1oZWFsdGhf5Ymv5pysMDHniYgvY29tcG9uZW50cy9hZHZhbmNlZC9BcHBQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2Fadvanced%2FAppProvider.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/BreathingExercise.tsx":
/*!******************************************!*\
  !*** ./components/BreathingExercise.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BreathingExercise)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction BreathingExercise({ locale }) {\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPhase, setCurrentPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isComplete, setIsComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const phases = [\n        {\n            name: \"吸气\",\n            nameEn: \"Inhale\",\n            duration: 4,\n            color: \"bg-blue-600\"\n        },\n        {\n            name: \"屏息\",\n            nameEn: \"Hold\",\n            duration: 7,\n            color: \"bg-purple-600\"\n        },\n        {\n            name: \"呼气\",\n            nameEn: \"Exhale\",\n            duration: 8,\n            color: \"bg-pink-600\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let interval;\n        if (isActive && timeLeft > 0) {\n            interval = setInterval(()=>{\n                setTimeLeft(timeLeft - 1);\n            }, 1000);\n        } else if (isActive && timeLeft === 0) {\n            if (currentPhase < phases.length - 1) {\n                // Move to next phase\n                setTimeout(()=>{\n                    setCurrentPhase(currentPhase + 1);\n                    setTimeLeft(phases[currentPhase + 1].duration);\n                }, 500);\n            } else {\n                // Complete the cycle\n                setIsActive(false);\n                setIsComplete(true);\n                setCurrentPhase(0);\n            }\n        }\n        return ()=>clearInterval(interval);\n    }, [\n        isActive,\n        timeLeft,\n        currentPhase,\n        phases\n    ]);\n    const startExercise = ()=>{\n        setIsActive(true);\n        setIsComplete(false);\n        setCurrentPhase(0);\n        setTimeLeft(phases[0].duration);\n    };\n    const resetExercise = ()=>{\n        setIsActive(false);\n        setIsComplete(false);\n        setCurrentPhase(0);\n        setTimeLeft(0);\n    };\n    const getCurrentPhase = ()=>phases[currentPhase];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg p-6 border border-blue-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-2xl\",\n                            children: \"\\uD83E\\uDEC1\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold text-blue-800 mb-2\",\n                        children: locale === \"en\" ? \"4-7-8 Breathing Exercise\" : \"4-7-8 深呼吸练习\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-600 text-sm\",\n                        children: locale === \"en\" ? \"Natural pain relief through nervous system regulation\" : \"通过调节神经系统自然缓解疼痛\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-semibold text-blue-800 mb-2\",\n                        children: locale === \"en\" ? \"How to practice:\" : \"练习方法：\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-3 text-center text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-blue-600\",\n                                            children: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-700\",\n                                        children: [\n                                            locale === \"en\" ? \"Inhale\" : \"吸气\",\n                                            \" 4\",\n                                            locale === \"en\" ? \"s\" : \"秒\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-purple-600\",\n                                            children: \"7\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-purple-700\",\n                                        children: [\n                                            locale === \"en\" ? \"Hold\" : \"屏息\",\n                                            \" 7\",\n                                            locale === \"en\" ? \"s\" : \"秒\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-pink-600\",\n                                            children: \"8\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-pink-700\",\n                                        children: [\n                                            locale === \"en\" ? \"Exhale\" : \"呼气\",\n                                            \" 8\",\n                                            locale === \"en\" ? \"s\" : \"秒\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-6\",\n                children: isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `w-32 h-32 rounded-full flex items-center justify-center mx-auto transition-all duration-1000 ${getCurrentPhase().color}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold\",\n                                        children: timeLeft\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: locale === \"zh\" ? getCurrentPhase().name : getCurrentPhase().nameEn\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                locale === \"en\" ? \"Current:\" : \"正在进行：\",\n                                \" \",\n                                locale === \"zh\" ? getCurrentPhase().name : getCurrentPhase().nameEn\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-4xl text-gray-400\",\n                        children: \"\\uD83E\\uDEC1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-3\",\n                children: [\n                    !isActive && !isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: startExercise,\n                        className: \"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors w-full\",\n                        children: locale === \"en\" ? \"\\uD83E\\uDEC1 Start Guided Practice\" : \"\\uD83E\\uDEC1 开始引导练习\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-700 font-medium\",\n                                    children: locale === \"en\" ? \"✅ One cycle completed!\" : \"✅ 一轮练习完成！\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: startExercise,\n                                className: \"bg-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors w-full\",\n                                children: locale === \"en\" ? \"Practice Again\" : \"再次练习\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: resetExercise,\n                        className: \"bg-gray-500 text-white px-6 py-2 rounded-lg font-medium hover:bg-gray-600 transition-colors\",\n                        children: locale === \"en\" ? \"Stop Practice\" : \"停止练习\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                        className: \"font-semibold text-gray-800 mb-2\",\n                        children: locale === \"en\" ? \"Scientific Benefits:\" : \"科学效果：\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-3 text-center text-xs\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-blue-600\",\n                                        children: \"-40%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: locale === \"en\" ? \"Pain Perception\" : \"疼痛感知\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-purple-600\",\n                                        children: \"-35%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: locale === \"en\" ? \"Muscle Tension\" : \"肌肉紧张\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-pink-600\",\n                                        children: \"+60%\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: locale === \"en\" ? \"Relaxation\" : \"放松感受\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 text-xs text-gray-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: locale === \"en\" ? \"\\uD83D\\uDCA1 Tip: Find a comfortable sitting or lying position, relax all muscles. Beginners should do 3-4 cycles.\" : \"\\uD83D\\uDCA1 建议：找一个舒适的坐位或躺位，放松全身肌肉。初学者建议进行3-4个循环。\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0JyZWF0aGluZ0V4ZXJjaXNlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFbUQ7QUFhcEMsU0FBU0csa0JBQWtCLEVBQUVDLE1BQU0sRUFBMEI7SUFDMUUsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdMLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ00sY0FBY0MsZ0JBQWdCLEdBQUdQLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ1EsVUFBVUMsWUFBWSxHQUFHVCwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNVLFlBQVlDLGNBQWMsR0FBR1gsK0NBQVFBLENBQUM7SUFFN0MsTUFBTVksU0FBa0I7UUFDdEI7WUFDRUMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsT0FBTztRQUNUO1FBQ0E7WUFDRUgsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsT0FBTztRQUNUO1FBQ0E7WUFDRUgsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLFVBQVU7WUFDVkMsT0FBTztRQUNUO0tBQ0Q7SUFFRGYsZ0RBQVNBLENBQUM7UUFDUixJQUFJZ0I7UUFFSixJQUFJYixZQUFZSSxXQUFXLEdBQUc7WUFDNUJTLFdBQVdDLFlBQVk7Z0JBQ3JCVCxZQUFZRCxXQUFXO1lBQ3pCLEdBQUc7UUFDTCxPQUFPLElBQUlKLFlBQVlJLGFBQWEsR0FBRztZQUNyQyxJQUFJRixlQUFlTSxPQUFPTyxNQUFNLEdBQUcsR0FBRztnQkFDcEMscUJBQXFCO2dCQUNyQkMsV0FBVztvQkFDVGIsZ0JBQWdCRCxlQUFlO29CQUMvQkcsWUFBWUcsTUFBTSxDQUFDTixlQUFlLEVBQUUsQ0FBQ1MsUUFBUTtnQkFDL0MsR0FBRztZQUNMLE9BQU87Z0JBQ0wscUJBQXFCO2dCQUNyQlYsWUFBWTtnQkFDWk0sY0FBYztnQkFDZEosZ0JBQWdCO1lBQ2xCO1FBQ0Y7UUFFQSxPQUFPLElBQU1jLGNBQWNKO0lBQzdCLEdBQUc7UUFBQ2I7UUFBVUk7UUFBVUY7UUFBY007S0FBTztJQUU3QyxNQUFNVSxnQkFBZ0I7UUFDcEJqQixZQUFZO1FBQ1pNLGNBQWM7UUFDZEosZ0JBQWdCO1FBQ2hCRSxZQUFZRyxNQUFNLENBQUMsRUFBRSxDQUFDRyxRQUFRO0lBQ2hDO0lBRUEsTUFBTVEsZ0JBQWdCO1FBQ3BCbEIsWUFBWTtRQUNaTSxjQUFjO1FBQ2RKLGdCQUFnQjtRQUNoQkUsWUFBWTtJQUNkO0lBRUEsTUFBTWUsa0JBQWtCLElBQU1aLE1BQU0sQ0FBQ04sYUFBYTtJQUVsRCxxQkFDRSw4REFBQ21CO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDQzs0QkFBS0QsV0FBVTtzQ0FBVzs7Ozs7Ozs7Ozs7a0NBRTdCLDhEQUFDRTt3QkFBR0YsV0FBVTtrQ0FDWHZCLFdBQVcsT0FBTyw2QkFBNkI7Ozs7OztrQ0FFbEQsOERBQUMwQjt3QkFBRUgsV0FBVTtrQ0FDVnZCLFdBQVcsT0FDUiwwREFDQTs7Ozs7Ozs7Ozs7OzBCQU1SLDhEQUFDc0I7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDSTt3QkFBR0osV0FBVTtrQ0FDWHZCLFdBQVcsT0FBTyxxQkFBcUI7Ozs7OztrQ0FFMUMsOERBQUNzQjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEOztrREFDQyw4REFBQ0E7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNDOzRDQUFLRCxXQUFVO3NEQUFrQzs7Ozs7Ozs7Ozs7a0RBRXBELDhEQUFDRzt3Q0FBRUgsV0FBVTs7NENBQ1Z2QixXQUFXLE9BQU8sV0FBVzs0Q0FBSzs0Q0FBR0EsV0FBVyxPQUFPLE1BQU07Ozs7Ozs7Ozs7Ozs7MENBR2xFLDhEQUFDc0I7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0M7NENBQUtELFdBQVU7c0RBQW9DOzs7Ozs7Ozs7OztrREFFdEQsOERBQUNHO3dDQUFFSCxXQUFVOzs0Q0FDVnZCLFdBQVcsT0FBTyxTQUFTOzRDQUFLOzRDQUFHQSxXQUFXLE9BQU8sTUFBTTs7Ozs7Ozs7Ozs7OzswQ0FHaEUsOERBQUNzQjs7a0RBQ0MsOERBQUNBO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDQzs0Q0FBS0QsV0FBVTtzREFBa0M7Ozs7Ozs7Ozs7O2tEQUVwRCw4REFBQ0c7d0NBQUVILFdBQVU7OzRDQUNWdkIsV0FBVyxPQUFPLFdBQVc7NENBQUs7NENBQUdBLFdBQVcsT0FBTyxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU90RSw4REFBQ3NCO2dCQUFJQyxXQUFVOzBCQUNadEIseUJBQ0MsOERBQUNxQjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFXLENBQUMsNkZBQTZGLEVBQUVGLGtCQUFrQlIsS0FBSyxDQUFDLENBQUM7c0NBQ3ZJLDRFQUFDUztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUFzQmxCOzs7Ozs7a0RBQ3JDLDhEQUFDaUI7d0NBQUlDLFdBQVU7a0RBQ1p2QixXQUFXLE9BQU9xQixrQkFBa0JYLElBQUksR0FBR1csa0JBQWtCVixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJMUUsOERBQUNlOzRCQUFFSCxXQUFVOztnQ0FDVnZCLFdBQVcsT0FBTyxhQUFhO2dDQUFRO2dDQUFFQSxXQUFXLE9BQU9xQixrQkFBa0JYLElBQUksR0FBR1csa0JBQWtCVixNQUFNOzs7Ozs7Ozs7Ozs7eUNBSWpILDhEQUFDVztvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUtELFdBQVU7a0NBQXlCOzs7Ozs7Ozs7Ozs7Ozs7OzBCQU0vQyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O29CQUNaLENBQUN0QixZQUFZLENBQUNNLDRCQUNiLDhEQUFDcUI7d0JBQ0NDLFNBQVNWO3dCQUNUSSxXQUFVO2tDQUVUdkIsV0FBVyxPQUFPLHVDQUE2Qjs7Ozs7O29CQUluRE8sNEJBQ0MsOERBQUNlO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNHO29DQUFFSCxXQUFVOzhDQUNWdkIsV0FBVyxPQUFPLDJCQUEyQjs7Ozs7Ozs7Ozs7MENBR2xELDhEQUFDNEI7Z0NBQ0NDLFNBQVNWO2dDQUNUSSxXQUFVOzBDQUVUdkIsV0FBVyxPQUFPLG1CQUFtQjs7Ozs7Ozs7Ozs7O29CQUszQ0MsMEJBQ0MsOERBQUMyQjt3QkFDQ0MsU0FBU1Q7d0JBQ1RHLFdBQVU7a0NBRVR2QixXQUFXLE9BQU8sa0JBQWtCOzs7Ozs7Ozs7Ozs7MEJBTTNDLDhEQUFDc0I7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDTzt3QkFBR1AsV0FBVTtrQ0FDWHZCLFdBQVcsT0FBTyx5QkFBeUI7Ozs7OztrQ0FFOUMsOERBQUNzQjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEOztrREFDQyw4REFBQ0E7d0NBQUlDLFdBQVU7a0RBQWtDOzs7Ozs7a0RBQ2pELDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWnZCLFdBQVcsT0FBTyxvQkFBb0I7Ozs7Ozs7Ozs7OzswQ0FHM0MsOERBQUNzQjs7a0RBQ0MsOERBQUNBO3dDQUFJQyxXQUFVO2tEQUFvQzs7Ozs7O2tEQUNuRCw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1p2QixXQUFXLE9BQU8sbUJBQW1COzs7Ozs7Ozs7Ozs7MENBRzFDLDhEQUFDc0I7O2tEQUNDLDhEQUFDQTt3Q0FBSUMsV0FBVTtrREFBa0M7Ozs7OztrREFDakQsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNadkIsV0FBVyxPQUFPLGVBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPMUMsOERBQUNzQjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0c7OEJBQ0UxQixXQUFXLE9BQ1IsdUhBQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTWQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZXJpb2RodWItaGVhbHRoLy4vY29tcG9uZW50cy9CcmVhdGhpbmdFeGVyY2lzZS50c3g/MTEyNSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgQnJlYXRoaW5nRXhlcmNpc2VQcm9wcyB7XG4gIGxvY2FsZTogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgUGhhc2Uge1xuICBuYW1lOiBzdHJpbmc7XG4gIG5hbWVFbjogc3RyaW5nO1xuICBkdXJhdGlvbjogbnVtYmVyO1xuICBjb2xvcjogc3RyaW5nO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBCcmVhdGhpbmdFeGVyY2lzZSh7IGxvY2FsZSB9OiBCcmVhdGhpbmdFeGVyY2lzZVByb3BzKSB7XG4gIGNvbnN0IFtpc0FjdGl2ZSwgc2V0SXNBY3RpdmVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY3VycmVudFBoYXNlLCBzZXRDdXJyZW50UGhhc2VdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFt0aW1lTGVmdCwgc2V0VGltZUxlZnRdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtpc0NvbXBsZXRlLCBzZXRJc0NvbXBsZXRlXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCBwaGFzZXM6IFBoYXNlW10gPSBbXG4gICAge1xuICAgICAgbmFtZTogJ+WQuOawlCcsXG4gICAgICBuYW1lRW46ICdJbmhhbGUnLFxuICAgICAgZHVyYXRpb246IDQsXG4gICAgICBjb2xvcjogJ2JnLWJsdWUtNjAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ+Wxj+aBrycsXG4gICAgICBuYW1lRW46ICdIb2xkJyxcbiAgICAgIGR1cmF0aW9uOiA3LFxuICAgICAgY29sb3I6ICdiZy1wdXJwbGUtNjAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ+WRvOawlCcsXG4gICAgICBuYW1lRW46ICdFeGhhbGUnLFxuICAgICAgZHVyYXRpb246IDgsXG4gICAgICBjb2xvcjogJ2JnLXBpbmstNjAwJ1xuICAgIH1cbiAgXTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxldCBpbnRlcnZhbDogTm9kZUpTLlRpbWVvdXQ7XG5cbiAgICBpZiAoaXNBY3RpdmUgJiYgdGltZUxlZnQgPiAwKSB7XG4gICAgICBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgICAgc2V0VGltZUxlZnQodGltZUxlZnQgLSAxKTtcbiAgICAgIH0sIDEwMDApO1xuICAgIH0gZWxzZSBpZiAoaXNBY3RpdmUgJiYgdGltZUxlZnQgPT09IDApIHtcbiAgICAgIGlmIChjdXJyZW50UGhhc2UgPCBwaGFzZXMubGVuZ3RoIC0gMSkge1xuICAgICAgICAvLyBNb3ZlIHRvIG5leHQgcGhhc2VcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgc2V0Q3VycmVudFBoYXNlKGN1cnJlbnRQaGFzZSArIDEpO1xuICAgICAgICAgIHNldFRpbWVMZWZ0KHBoYXNlc1tjdXJyZW50UGhhc2UgKyAxXS5kdXJhdGlvbik7XG4gICAgICAgIH0sIDUwMCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBDb21wbGV0ZSB0aGUgY3ljbGVcbiAgICAgICAgc2V0SXNBY3RpdmUoZmFsc2UpO1xuICAgICAgICBzZXRJc0NvbXBsZXRlKHRydWUpO1xuICAgICAgICBzZXRDdXJyZW50UGhhc2UoMCk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICB9LCBbaXNBY3RpdmUsIHRpbWVMZWZ0LCBjdXJyZW50UGhhc2UsIHBoYXNlc10pO1xuXG4gIGNvbnN0IHN0YXJ0RXhlcmNpc2UgPSAoKSA9PiB7XG4gICAgc2V0SXNBY3RpdmUodHJ1ZSk7XG4gICAgc2V0SXNDb21wbGV0ZShmYWxzZSk7XG4gICAgc2V0Q3VycmVudFBoYXNlKDApO1xuICAgIHNldFRpbWVMZWZ0KHBoYXNlc1swXS5kdXJhdGlvbik7XG4gIH07XG5cbiAgY29uc3QgcmVzZXRFeGVyY2lzZSA9ICgpID0+IHtcbiAgICBzZXRJc0FjdGl2ZShmYWxzZSk7XG4gICAgc2V0SXNDb21wbGV0ZShmYWxzZSk7XG4gICAgc2V0Q3VycmVudFBoYXNlKDApO1xuICAgIHNldFRpbWVMZWZ0KDApO1xuICB9O1xuXG4gIGNvbnN0IGdldEN1cnJlbnRQaGFzZSA9ICgpID0+IHBoYXNlc1tjdXJyZW50UGhhc2VdO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1sZyBwLTYgYm9yZGVyIGJvcmRlci1ibHVlLTEwMFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ibHVlLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGxcIj7wn6uBPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtODAwIG1iLTJcIj5cbiAgICAgICAgICB7bG9jYWxlID09PSAnZW4nID8gJzQtNy04IEJyZWF0aGluZyBFeGVyY2lzZScgOiAnNC03LTgg5rex5ZG85ZC457uD5LmgJ31cbiAgICAgICAgPC9oMz5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAge2xvY2FsZSA9PT0gJ2VuJ1xuICAgICAgICAgICAgPyAnTmF0dXJhbCBwYWluIHJlbGllZiB0aHJvdWdoIG5lcnZvdXMgc3lzdGVtIHJlZ3VsYXRpb24nXG4gICAgICAgICAgICA6ICfpgJrov4fosIPoioLnpZ7nu4/ns7vnu5/oh6rnhLbnvJPop6Pnlrznl5snXG4gICAgICAgICAgfVxuICAgICAgICA8L3A+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEluc3RydWN0aW9ucyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCByb3VuZGVkLWxnIHAtNCBtYi02XCI+XG4gICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtYmx1ZS04MDAgbWItMlwiPlxuICAgICAgICAgIHtsb2NhbGUgPT09ICdlbicgPyAnSG93IHRvIHByYWN0aWNlOicgOiAn57uD5Lmg5pa55rOV77yaJ31cbiAgICAgICAgPC9oND5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0zIGdhcC0zIHRleHQtY2VudGVyIHRleHQtc21cIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctYmx1ZS0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItMlwiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwXCI+NDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTcwMFwiPlxuICAgICAgICAgICAgICB7bG9jYWxlID09PSAnZW4nID8gJ0luaGFsZScgOiAn5ZC45rCUJ30gNHtsb2NhbGUgPT09ICdlbicgPyAncycgOiAn56eSJ31cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctcHVycGxlLTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi0yXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtcHVycGxlLTYwMFwiPjc8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcHVycGxlLTcwMFwiPlxuICAgICAgICAgICAgICB7bG9jYWxlID09PSAnZW4nID8gJ0hvbGQnIDogJ+Wxj+aBryd9IDd7bG9jYWxlID09PSAnZW4nID8gJ3MnIDogJ+enkid9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMiBoLTEyIGJnLXBpbmstMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTJcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC1waW5rLTYwMFwiPjg8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcGluay03MDBcIj5cbiAgICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2VuJyA/ICdFeGhhbGUnIDogJ+WRvOawlCd9IDh7bG9jYWxlID09PSAnZW4nID8gJ3MnIDogJ+enkid9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBFeGVyY2lzZSBEaXNwbGF5ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi02XCI+XG4gICAgICAgIHtpc0FjdGl2ZSA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTMyIGgtMzIgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMTAwMCAke2dldEN1cnJlbnRQaGFzZSgpLmNvbG9yfWB9PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZFwiPnt0aW1lTGVmdH08L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIHtsb2NhbGUgPT09ICd6aCcgPyBnZXRDdXJyZW50UGhhc2UoKS5uYW1lIDogZ2V0Q3VycmVudFBoYXNlKCkubmFtZUVufVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICB7bG9jYWxlID09PSAnZW4nID8gJ0N1cnJlbnQ6JyA6ICfmraPlnKjov5vooYzvvJonfSB7bG9jYWxlID09PSAnemgnID8gZ2V0Q3VycmVudFBoYXNlKCkubmFtZSA6IGdldEN1cnJlbnRQaGFzZSgpLm5hbWVFbn1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMzIgaC0zMiBiZy1ncmF5LTEwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0b1wiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC00eGwgdGV4dC1ncmF5LTQwMFwiPvCfq4E8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIENvbnRyb2wgQnV0dG9ucyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc3BhY2UteS0zXCI+XG4gICAgICAgIHshaXNBY3RpdmUgJiYgIWlzQ29tcGxldGUgJiYgKFxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e3N0YXJ0RXhlcmNpc2V9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHB4LTggcHktMyByb3VuZGVkLWxnIGZvbnQtc2VtaWJvbGQgaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnMgdy1mdWxsXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7bG9jYWxlID09PSAnZW4nID8gJ/Cfq4EgU3RhcnQgR3VpZGVkIFByYWN0aWNlJyA6ICfwn6uBIOW8gOWni+W8leWvvOe7g+S5oCd9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICl9XG5cbiAgICAgICAge2lzQ29tcGxldGUgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIHJvdW5kZWQtbGcgcC0zXCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNzAwIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2VuJyA/ICfinIUgT25lIGN5Y2xlIGNvbXBsZXRlZCEnIDogJ+KchSDkuIDova7nu4PkuaDlrozmiJDvvIEnfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17c3RhcnRFeGVyY2lzZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcHVycGxlLTYwMCB0ZXh0LXdoaXRlIHB4LTggcHktMyByb3VuZGVkLWxnIGZvbnQtc2VtaWJvbGQgaG92ZXI6YmctcHVycGxlLTcwMCB0cmFuc2l0aW9uLWNvbG9ycyB3LWZ1bGxcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7bG9jYWxlID09PSAnZW4nID8gJ1ByYWN0aWNlIEFnYWluJyA6ICflho3mrKHnu4PkuaAnfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAge2lzQWN0aXZlICYmIChcbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtyZXNldEV4ZXJjaXNlfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JheS01MDAgdGV4dC13aGl0ZSBweC02IHB5LTIgcm91bmRlZC1sZyBmb250LW1lZGl1bSBob3ZlcjpiZy1ncmF5LTYwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2VuJyA/ICdTdG9wIFByYWN0aWNlJyA6ICflgZzmraLnu4PkuaAnfVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBCZW5lZml0cyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNiBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MCB0by1wdXJwbGUtNTAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgPGg1IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBtYi0yXCI+XG4gICAgICAgICAge2xvY2FsZSA9PT0gJ2VuJyA/ICdTY2llbnRpZmljIEJlbmVmaXRzOicgOiAn56eR5a2m5pWI5p6c77yaJ31cbiAgICAgICAgPC9oNT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0zIGdhcC0zIHRleHQtY2VudGVyIHRleHQteHNcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwXCI+LTQwJTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIHtsb2NhbGUgPT09ICdlbicgPyAnUGFpbiBQZXJjZXB0aW9uJyA6ICfnlrznl5vmhJ/nn6UnfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC1wdXJwbGUtNjAwXCI+LTM1JTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIHtsb2NhbGUgPT09ICdlbicgPyAnTXVzY2xlIFRlbnNpb24nIDogJ+iCjOiCiee0p+W8oCd9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZCB0ZXh0LXBpbmstNjAwXCI+KzYwJTwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIHtsb2NhbGUgPT09ICdlbicgPyAnUmVsYXhhdGlvbicgOiAn5pS+5p2+5oSf5Y+XJ31cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogVGlwcyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgPHA+XG4gICAgICAgICAge2xvY2FsZSA9PT0gJ2VuJ1xuICAgICAgICAgICAgPyAn8J+SoSBUaXA6IEZpbmQgYSBjb21mb3J0YWJsZSBzaXR0aW5nIG9yIGx5aW5nIHBvc2l0aW9uLCByZWxheCBhbGwgbXVzY2xlcy4gQmVnaW5uZXJzIHNob3VsZCBkbyAzLTQgY3ljbGVzLidcbiAgICAgICAgICAgIDogJ/CfkqEg5bu66K6u77ya5om+5LiA5Liq6IiS6YCC55qE5Z2Q5L2N5oiW6Lq65L2N77yM5pS+5p2+5YWo6Lqr6IKM6IKJ44CC5Yid5a2m6ICF5bu66K6u6L+b6KGMMy005Liq5b6q546v44CCJ1xuICAgICAgICAgIH1cbiAgICAgICAgPC9wPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkJyZWF0aGluZ0V4ZXJjaXNlIiwibG9jYWxlIiwiaXNBY3RpdmUiLCJzZXRJc0FjdGl2ZSIsImN1cnJlbnRQaGFzZSIsInNldEN1cnJlbnRQaGFzZSIsInRpbWVMZWZ0Iiwic2V0VGltZUxlZnQiLCJpc0NvbXBsZXRlIiwic2V0SXNDb21wbGV0ZSIsInBoYXNlcyIsIm5hbWUiLCJuYW1lRW4iLCJkdXJhdGlvbiIsImNvbG9yIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsImxlbmd0aCIsInNldFRpbWVvdXQiLCJjbGVhckludGVydmFsIiwic3RhcnRFeGVyY2lzZSIsInJlc2V0RXhlcmNpc2UiLCJnZXRDdXJyZW50UGhhc2UiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwiaDMiLCJwIiwiaDQiLCJidXR0b24iLCJvbkNsaWNrIiwiaDUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/BreathingExercise.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Footer() {\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useLocale)();\n    const currentYear = new Date().getFullYear();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-neutral-100 border-t border-neutral-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center md:items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: `/${locale}`,\n                                    className: \"font-bold text-lg text-primary-600 hover:text-primary-700 transition-colors\",\n                                    children: \"periodhub.health\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-sm text-neutral-600 max-w-xs\",\n                                    children: locale === \"en\" ? \"Your trusted menstrual health companion\" : \"您值得信赖的经期健康伙伴\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center md:items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-neutral-800 mb-4\",\n                                    children: locale === \"en\" ? \"Links\" : \"链接\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex flex-col space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: `/${locale}/privacy-policy`,\n                                            className: \"text-sm text-neutral-600 hover:text-primary-600 transition-colors\",\n                                            children: locale === \"en\" ? \"Privacy Policy\" : \"隐私政策\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: `/${locale}/terms-of-service`,\n                                            className: \"text-sm text-neutral-600 hover:text-primary-600 transition-colors\",\n                                            children: locale === \"en\" ? \"Terms of Service\" : \"服务条款\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: `/${locale}/medical-disclaimer`,\n                                            className: \"text-sm text-neutral-600 hover:text-primary-600 transition-colors\",\n                                            children: locale === \"en\" ? \"Medical Disclaimer\" : \"医疗免责声明\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: `/${locale}/articles`,\n                                            className: \"text-sm text-neutral-600 hover:text-primary-600 transition-colors\",\n                                            children: locale === \"en\" ? \"Articles\" : \"文章PDF下载中心\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: `/${locale}/natural-therapies`,\n                                            className: \"text-sm text-neutral-600 hover:text-primary-600 transition-colors\",\n                                            children: locale === \"en\" ? \"Natural Therapies\" : \"平时调理\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center md:items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-neutral-800 mb-4\",\n                                    children: locale === \"en\" ? \"Contact\" : \"联系我们\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"mailto:<EMAIL>\",\n                                    className: \"text-sm text-neutral-600 hover:text-primary-600 transition-colors\",\n                                    children: \"<EMAIL>\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-neutral-600 hover:text-primary-600 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Twitter\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-neutral-600 hover:text-primary-600 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Instagram\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    \"aria-hidden\": \"true\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t border-neutral-200 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-neutral-500\",\n                            children: locale === \"en\" ? `© ${currentYear} Period Hub Platform. All rights reserved.` : `© ${currentYear} 痛经健康平台。保留所有权利。`\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-xs text-neutral-500 max-w-2xl mx-auto\",\n                            children: locale === \"en\" ? \"Medical Disclaimer: The content on this website is for informational and educational purposes only and is not intended to be a substitute for professional medical advice, diagnosis, or treatment. We are not healthcare professionals. Always seek the advice of your physician or other qualified health provider with any questions you may have regarding a medical condition. In case of emergency, seek immediate medical attention. Use of this website does not establish a doctor-patient relationship.\" : \"医疗免责声明：本网站内容仅供信息和教育目的，不能替代专业医疗建议、诊断或治疗。我们不是医疗专业人员。如有任何医疗问题，请咨询您的医生或其他合格的医疗服务提供者。紧急情况下，请立即寻求医疗救助。使用本网站不构成医患关系。\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Header() {\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useLocale)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // Navigation items\n    const navigation = [\n        {\n            name: locale === \"en\" ? \"Home\" : \"首页\",\n            href: `/${locale}`\n        },\n        {\n            name: locale === \"en\" ? \"Interactive Solutions\" : \"互动解决方案\",\n            href: `/${locale}/interactive-tools`\n        },\n        {\n            name: locale === \"en\" ? \"Articles & Downloads\" : \"文章PDF下载中心\",\n            href: `/${locale}/articles`\n        },\n        {\n            name: locale === \"en\" ? \"Scenario Solutions\" : \"场景解决方案\",\n            href: `/${locale}/scenario-solutions`\n        },\n        // { name: locale === 'en' ? '🚀 Framework Demo' : '🚀 框架演示', href: `/${locale}/framework-demo` }, // 暂时隐藏 - 可快速恢复\n        {\n            name: locale === \"en\" ? \"Natural Care\" : \"平时调理\",\n            href: `/${locale}/natural-therapies`\n        },\n        {\n            name: locale === \"en\" ? \"Health Guide\" : \"痛经健康指南\",\n            href: `/${locale}/health-guide`\n        }\n    ];\n    // Handle scroll effect for header\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            if (window.scrollY > 10) {\n                setScrolled(true);\n            } else {\n                setScrolled(false);\n            }\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Close mobile menu on route change\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setIsMenuOpen(false);\n    }, [\n        pathname\n    ]);\n    // Check if a nav item is active\n    const isActive = (href)=>{\n        if (href === `/${locale}`) {\n            return pathname === href;\n        }\n        return pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: `sticky top-0 z-40 w-full transition-all duration-300 ${scrolled ? \"bg-white/95 backdrop-blur-md shadow-sm border-b border-neutral-200/80\" : \"bg-white/85 backdrop-blur-sm\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-14 sm:h-16 md:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: `/${locale}`,\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold text-lg sm:text-xl text-primary-600 hover:text-primary-700 transition-colors\",\n                                    children: \"periodhub.health\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-1 lg:space-x-2\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: item.href,\n                                    className: `px-2 lg:px-3 py-2 text-xs lg:text-sm font-medium rounded-md transition-colors ${isActive(item.href) ? \"bg-primary-50 text-primary-600\" : \"text-neutral-600 hover:text-primary-600 hover:bg-primary-50\"}`,\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1 sm:space-x-2 md:space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageSwitcher, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                    className: \"md:hidden inline-flex items-center justify-center p-2 rounded-md text-neutral-600 hover:text-primary-600 hover:bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-colors min-w-[44px] min-h-[44px]\",\n                                    \"aria-controls\": \"mobile-menu\",\n                                    \"aria-expanded\": isMenuOpen,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Open main menu\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        !isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"block h-5 w-5 sm:h-6 sm:w-6\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"block h-5 w-5 sm:h-6 sm:w-6\",\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden border-t border-neutral-200 bg-white/95 backdrop-blur-md\",\n                    id: \"mobile-menu\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-3 pb-4 space-y-2 sm:px-3\",\n                        children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: item.href,\n                                className: `block px-4 py-3 rounded-lg text-base font-medium transition-colors min-h-[44px] flex items-center ${isActive(item.href) ? \"bg-primary-50 text-primary-600 border border-primary-200\" : \"text-neutral-700 hover:bg-primary-50 hover:text-primary-600 border border-transparent\"}`,\n                                children: item.name\n                            }, item.name, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n// Language Switcher Component\nfunction LanguageSwitcher() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useLocale)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const languages = [\n        {\n            code: \"en\",\n            name: \"English\",\n            flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n        },\n        {\n            code: \"zh\",\n            name: \"中文\",\n            flag: \"\\uD83C\\uDDE8\\uD83C\\uDDF3\"\n        }\n    ];\n    const currentLanguage = languages.find((lang)=>lang.code === locale);\n    const switchLocale = (newLocale)=>{\n        // Replace the current locale segment in the pathname with the new locale\n        const newPath = pathname.replace(`/${locale}`, `/${newLocale}`);\n        window.location.href = newPath; // Use window.location for a full page refresh\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-1 px-2 py-2 text-sm font-medium text-neutral-600 hover:text-primary-600 rounded hover:bg-neutral-100 transition-colors min-w-[44px] min-h-[44px] justify-center sm:justify-start\",\n                \"aria-expanded\": isOpen,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-base\",\n                        children: currentLanguage?.flag\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hidden sm:inline text-xs lg:text-sm\",\n                        children: currentLanguage?.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: `w-3 h-3 sm:w-4 sm:h-4 transition-transform ${isOpen ? \"rotate-180\" : \"\"} hidden sm:block`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-32 sm:w-40 bg-white rounded-md shadow-lg border border-neutral-200 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1\",\n                    children: languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>switchLocale(language.code),\n                            className: `flex items-center space-x-2 w-full px-3 sm:px-4 py-3 text-sm text-left hover:bg-neutral-50 transition-colors min-h-[44px] ${locale === language.code ? \"bg-primary-50 text-primary-600\" : \"text-neutral-700\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-base\",\n                                    children: language.flag\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: language.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, language.code, true, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/advanced/AppProvider.tsx":
/*!*********************************************!*\
  !*** ./components/advanced/AppProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppProvider: () => (/* binding */ AppProvider),\n/* harmony export */   ComponentProvider: () => (/* binding */ ComponentProvider),\n/* harmony export */   PageProvider: () => (/* binding */ PageProvider),\n/* harmony export */   usePagePerformance: () => (/* binding */ usePagePerformance),\n/* harmony export */   useUserTracking: () => (/* binding */ useUserTracking)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ErrorBoundary */ \"(ssr)/./components/advanced/ErrorBoundary.tsx\");\n/* harmony import */ var _ToastSystem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ToastSystem */ \"(ssr)/./components/advanced/ToastSystem.tsx\");\n/* harmony import */ var _ModalSystem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ModalSystem */ \"(ssr)/./components/advanced/ModalSystem.tsx\");\n/* harmony import */ var _lib_stores_appStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/stores/appStore */ \"(ssr)/./lib/stores/appStore.ts\");\n/* harmony import */ var _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../lib/performance/monitor */ \"(ssr)/./lib/performance/monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ AppProvider,PageProvider,ComponentProvider,usePagePerformance,useUserTracking auto */ \n\n\n\n\n\n\n// 应用级别的Provider组件\nconst AppProvider = ({ children })=>{\n    const preferences = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_5__.useAppStore)((state)=>state.preferences);\n    const recordPageLoadTime = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_5__.useAppStore)((state)=>state.recordPageLoadTime);\n    // 初始化应用\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 记录页面加载时间\n        const loadTime = performance.now();\n        recordPageLoadTime(loadTime);\n        // 应用主题\n        applyTheme(preferences.theme);\n        // 应用字体大小\n        applyFontSize(preferences.fontSize);\n        // 应用可访问性设置\n        applyAccessibilitySettings(preferences.accessibility);\n        // 启用性能监控\n        if (preferences.privacy.analytics) {\n            _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__.performanceMonitor.setEnabled(true);\n        }\n        // 清理函数\n        return ()=>{\n            _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__.performanceMonitor.cleanup();\n        };\n    }, []);\n    // 监听偏好设置变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        applyTheme(preferences.theme);\n    }, [\n        preferences.theme\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        applyFontSize(preferences.fontSize);\n    }, [\n        preferences.fontSize\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        applyAccessibilitySettings(preferences.accessibility);\n    }, [\n        preferences.accessibility\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__.performanceMonitor.setEnabled(preferences.privacy.analytics);\n    }, [\n        preferences.privacy.analytics\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n        level: \"critical\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"app-container\",\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToastSystem__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModalSystem__WEBPACK_IMPORTED_MODULE_4__.ModalManager, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n// 应用主题\nfunction applyTheme(theme) {\n    const root = document.documentElement;\n    if (theme === \"system\") {\n        const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n        theme = prefersDark ? \"dark\" : \"light\";\n    }\n    root.classList.remove(\"light\", \"dark\");\n    root.classList.add(theme);\n    // 更新meta标签\n    const metaTheme = document.querySelector('meta[name=\"theme-color\"]');\n    if (metaTheme) {\n        metaTheme.setAttribute(\"content\", theme === \"dark\" ? \"#1f2937\" : \"#ffffff\");\n    }\n}\n// 应用字体大小\nfunction applyFontSize(fontSize) {\n    const root = document.documentElement;\n    root.classList.remove(\"text-sm\", \"text-base\", \"text-lg\");\n    switch(fontSize){\n        case \"small\":\n            root.classList.add(\"text-sm\");\n            break;\n        case \"large\":\n            root.classList.add(\"text-lg\");\n            break;\n        default:\n            root.classList.add(\"text-base\");\n    }\n}\n// 应用可访问性设置\nfunction applyAccessibilitySettings(accessibility) {\n    const root = document.documentElement;\n    // 高对比度\n    if (accessibility.highContrast) {\n        root.classList.add(\"high-contrast\");\n    } else {\n        root.classList.remove(\"high-contrast\");\n    }\n    // 减少动画\n    if (accessibility.reducedMotion) {\n        root.classList.add(\"reduce-motion\");\n    } else {\n        root.classList.remove(\"reduce-motion\");\n    }\n    // 屏幕阅读器优化\n    if (accessibility.screenReader) {\n        root.classList.add(\"screen-reader-optimized\");\n    } else {\n        root.classList.remove(\"screen-reader-optimized\");\n    }\n}\nconst PageProvider = ({ children, title, description, keywords = [] })=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 更新页面标题\n        if (title) {\n            document.title = `${title} - Period Hub Health`;\n        }\n        // 更新meta描述\n        if (description) {\n            const metaDescription = document.querySelector('meta[name=\"description\"]');\n            if (metaDescription) {\n                metaDescription.setAttribute(\"content\", description);\n            }\n        }\n        // 更新meta关键词\n        if (keywords.length > 0) {\n            const metaKeywords = document.querySelector('meta[name=\"keywords\"]');\n            if (metaKeywords) {\n                metaKeywords.setAttribute(\"content\", keywords.join(\", \"));\n            }\n        }\n    }, [\n        title,\n        description,\n        keywords\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n        level: \"page\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, undefined);\n};\nconst ComponentProvider = ({ children, name, fallback })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n        level: \"component\",\n        fallback: fallback,\n        onError: (error, errorInfo)=>{\n            console.error(`Component error in ${name}:`, error, errorInfo);\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, undefined);\n};\n// 性能监控Hook\nconst usePagePerformance = (pageName)=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const startTime = performance.now();\n        // 记录页面访问\n        _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__.performanceMonitor.recordCustomInteraction(\"navigation\", pageName);\n        return ()=>{\n            const endTime = performance.now();\n            const duration = endTime - startTime;\n            // 记录页面停留时间\n            _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__.performanceMonitor.recordCustomInteraction(\"navigation\", `${pageName}_duration`, duration);\n        };\n    }, [\n        pageName\n    ]);\n};\n// 用户行为追踪Hook\nconst useUserTracking = ()=>{\n    const recordInteraction = (type, element, data)=>{\n        _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__.performanceMonitor.recordCustomInteraction(type, element);\n        // 可以在这里添加更多的用户行为分析\n        console.log(\"User interaction:\", {\n            type,\n            element,\n            data,\n            timestamp: Date.now()\n        });\n    };\n    return {\n        recordInteraction\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/advanced/AppProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/advanced/ErrorBoundary.tsx":
/*!***********************************************!*\
  !*** ./components/advanced/ErrorBoundary.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _lib_stores_appStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/stores/appStore */ \"(ssr)/./lib/stores/appStore.ts\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,withErrorBoundary,useErrorHandler auto */ \n\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.retryCount = 0;\n        this.maxRetries = 3;\n        this.reportError = async (error, errorInfo)=>{\n            try {\n                // 这里可以发送到错误监控服务\n                const errorReport = {\n                    message: error.message,\n                    stack: error.stack,\n                    componentStack: errorInfo.componentStack,\n                    timestamp: new Date().toISOString(),\n                    url: window.location.href,\n                    userAgent: navigator.userAgent,\n                    errorId: this.state.errorId,\n                    level: this.props.level || \"component\"\n                };\n                // 发送到监控服务（示例）\n                // await fetch('/api/errors', {\n                //   method: 'POST',\n                //   headers: { 'Content-Type': 'application/json' },\n                //   body: JSON.stringify(errorReport),\n                // });\n                console.log(\"Error reported:\", errorReport);\n            } catch (reportingError) {\n                console.error(\"Failed to report error:\", reportingError);\n            }\n        };\n        this.handleRetry = ()=>{\n            if (this.retryCount < this.maxRetries) {\n                this.retryCount++;\n                this.setState({\n                    hasError: false,\n                    error: null,\n                    errorInfo: null,\n                    errorId: null\n                });\n            } else {\n                // 达到最大重试次数，刷新页面\n                window.location.reload();\n            }\n        };\n        this.handleGoHome = ()=>{\n            window.location.href = \"/\";\n        };\n        this.handleReportBug = ()=>{\n            const { error, errorInfo, errorId } = this.state;\n            const bugReport = {\n                errorId,\n                message: error?.message,\n                stack: error?.stack,\n                componentStack: errorInfo?.componentStack,\n                url: window.location.href,\n                timestamp: new Date().toISOString()\n            };\n            // 打开邮件客户端或错误报告页面\n            const subject = encodeURIComponent(`Bug Report: ${error?.message || \"Unknown Error\"}`);\n            const body = encodeURIComponent(`\nError ID: ${errorId}\nURL: ${window.location.href}\nTime: ${new Date().toISOString()}\n\nError Details:\n${error?.message}\n\nStack Trace:\n${error?.stack}\n\nComponent Stack:\n${errorInfo?.componentStack}\n\nPlease describe what you were doing when this error occurred:\n[Your description here]\n    `);\n            window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);\n        };\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null,\n            errorId: null\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error,\n            errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        this.setState({\n            errorInfo\n        });\n        // 记录错误到应用状态\n        _lib_stores_appStore__WEBPACK_IMPORTED_MODULE_2__.useAppStore.getState().incrementErrorCount();\n        // 调用自定义错误处理\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n        // 发送错误报告（在生产环境中）\n        if (false) {}\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            // 如果提供了自定义fallback，使用它\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            const { error, errorInfo, errorId } = this.state;\n            const { level = \"component\", showDetails = false } = this.props;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-[400px] flex items-center justify-center p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full bg-white rounded-lg shadow-lg border border-red-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-8 h-8 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: level === \"critical\" ? \"严重错误\" : \"出现错误\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: level === \"critical\" ? \"应用遇到了严重问题\" : \"这个组件遇到了问题\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-700 mb-2\",\n                                        children: \"我们很抱歉给您带来不便。错误已被记录，我们会尽快修复。\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this),\n                                    errorId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 font-mono bg-gray-50 p-2 rounded\",\n                                        children: [\n                                            \"错误ID: \",\n                                            errorId\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 19\n                                    }, this),\n                                    (showDetails || \"development\" === \"development\") && error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                        className: \"mt-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                className: \"text-sm text-gray-600 cursor-pointer hover:text-gray-800\",\n                                                children: \"查看技术详情\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 p-3 bg-gray-50 rounded text-xs font-mono\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"错误信息:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-red-600\",\n                                                                children: error.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    error.stack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"堆栈跟踪:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                className: \"whitespace-pre-wrap text-gray-600 max-h-32 overflow-y-auto\",\n                                                                children: error.stack\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    errorInfo?.componentStack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"组件堆栈:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                className: \"whitespace-pre-wrap text-gray-600 max-h-32 overflow-y-auto\",\n                                                                children: errorInfo.componentStack\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-2\",\n                                children: [\n                                    this.retryCount < this.maxRetries ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: this.handleRetry,\n                                        className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"重试 (\",\n                                                    this.maxRetries - this.retryCount,\n                                                    \" 次剩余)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.reload(),\n                                        className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"刷新页面\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: this.handleGoHome,\n                                                className: \"flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"返回首页\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: this.handleReportBug,\n                                                className: \"flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"报告问题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n// 高阶组件包装器\nfunction withErrorBoundary(Component, errorBoundaryProps) {\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            ...errorBoundaryProps,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n            lineNumber: 276,\n            columnNumber: 5\n        }, this);\n    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n    return WrappedComponent;\n}\n// Hook版本\nfunction useErrorHandler() {\n    const incrementErrorCount = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_2__.useAppStore)((state)=>state.incrementErrorCount);\n    return (error, errorInfo)=>{\n        incrementErrorCount();\n        console.error(\"Error caught by useErrorHandler:\", error, errorInfo);\n        // 可以在这里添加更多错误处理逻辑\n        if (false) {}\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/advanced/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./components/advanced/ModalSystem.tsx":
/*!*********************************************!*\
  !*** ./components/advanced/ModalSystem.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModalManager: () => (/* binding */ ModalManager),\n/* harmony export */   useModal: () => (/* binding */ useModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/stores/appStore */ \"(ssr)/./lib/stores/appStore.ts\");\n/* __next_internal_client_entry_do_not_use__ ModalManager,useModal auto */ \n\n\n\n\nconst Modal = ({ isOpen, onClose, type = \"default\", title, content, children, size = \"md\", closable = true, maskClosable = true, showFooter = false, confirmText = \"确认\", cancelText = \"取消\", onConfirm, onCancel, className = \"\", zIndex = 1000 })=>{\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            setIsVisible(true);\n            setIsAnimating(true);\n            document.body.style.overflow = \"hidden\";\n        } else {\n            setIsAnimating(false);\n            const timer = setTimeout(()=>{\n                setIsVisible(false);\n                document.body.style.overflow = \"\";\n            }, 200);\n            return ()=>clearTimeout(timer);\n        }\n        return ()=>{\n            document.body.style.overflow = \"\";\n        };\n    }, [\n        isOpen\n    ]);\n    const handleMaskClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.target === e.currentTarget && maskClosable) {\n            onClose();\n        }\n    }, [\n        maskClosable,\n        onClose\n    ]);\n    const handleConfirm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (onConfirm) {\n            try {\n                await onConfirm();\n                onClose();\n            } catch (error) {\n                console.error(\"Modal confirm error:\", error);\n            }\n        } else {\n            onClose();\n        }\n    }, [\n        onConfirm,\n        onClose\n    ]);\n    const handleCancel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (onCancel) {\n            onCancel();\n        }\n        onClose();\n    }, [\n        onCancel,\n        onClose\n    ]);\n    const getSizeClasses = ()=>{\n        switch(size){\n            case \"sm\":\n                return \"max-w-sm\";\n            case \"md\":\n                return \"max-w-md\";\n            case \"lg\":\n                return \"max-w-lg\";\n            case \"xl\":\n                return \"max-w-xl\";\n            case \"full\":\n                return \"max-w-full mx-4\";\n            default:\n                return \"max-w-md\";\n        }\n    };\n    const getTypeIcon = ()=>{\n        switch(type){\n            case \"confirm\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-6 h-6 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 16\n                }, undefined);\n            case \"alert\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-6 h-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    if (!isVisible) return null;\n    const modalContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed inset-0 flex items-center justify-center p-4`,\n        style: {\n            zIndex\n        },\n        onClick: handleMaskClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n          absolute inset-0 bg-black transition-opacity duration-200\n          ${isAnimating ? \"opacity-50\" : \"opacity-0\"}\n        `\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n          relative bg-white rounded-lg shadow-xl w-full ${getSizeClasses()}\n          transform transition-all duration-200\n          ${isAnimating ? \"scale-100 opacity-100\" : \"scale-95 opacity-0\"}\n          ${className}\n        `,\n                children: [\n                    (title || closable) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    getTypeIcon(),\n                                    title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined),\n                            closable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-1 text-gray-400 hover:text-gray-600 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: content || children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined),\n                    showFooter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end space-x-3 p-6 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCancel,\n                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors\",\n                                children: cancelText\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleConfirm,\n                                className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors\",\n                                children: confirmText\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(modalContent, document.body);\n};\n// 模态框管理器\nconst ModalManager = ()=>{\n    const modal = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.ui.modal);\n    const closeModal = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.closeModal);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Modal, {\n        isOpen: modal.isOpen,\n        onClose: closeModal,\n        ...modal.data\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, undefined);\n};\n// 模态框Hook\nconst useModal = ()=>{\n    const openModal = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.openModal);\n    const closeModal = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.closeModal);\n    const modal = {\n        open: (config)=>{\n            openModal(\"custom\", config);\n        },\n        close: ()=>{\n            closeModal();\n        },\n        confirm: (config)=>{\n            return new Promise((resolve)=>{\n                const modalConfig = {\n                    type: \"confirm\",\n                    title: config.title || \"确认操作\",\n                    content: config.content,\n                    showFooter: true,\n                    confirmText: config.confirmText || \"确认\",\n                    cancelText: config.cancelText || \"取消\",\n                    onConfirm: async ()=>{\n                        if (config.onConfirm) {\n                            await config.onConfirm();\n                        }\n                        resolve(true);\n                    },\n                    onCancel: ()=>{\n                        if (config.onCancel) {\n                            config.onCancel();\n                        }\n                        resolve(false);\n                    }\n                };\n                openModal(\"confirm\", modalConfig);\n            });\n        },\n        alert: (content, title)=>{\n            return new Promise((resolve)=>{\n                const modalConfig = {\n                    type: \"alert\",\n                    title: title || \"提示\",\n                    content,\n                    showFooter: true,\n                    confirmText: \"确定\",\n                    onConfirm: ()=>{\n                        resolve();\n                    }\n                };\n                openModal(\"alert\", modalConfig);\n            });\n        },\n        // 便捷方法\n        success: (content, title)=>{\n            return modal.alert(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-6 h-6 text-green-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: content\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, undefined), title || \"成功\");\n        },\n        error: (content, title)=>{\n            return modal.alert(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-6 h-6 text-red-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: content\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                lineNumber: 304,\n                columnNumber: 9\n            }, undefined), title || \"错误\");\n        },\n        warning: (content, title)=>{\n            return modal.confirm({\n                title: title || \"警告\",\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-6 h-6 text-yellow-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: content\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 11\n                }, undefined),\n                type: \"warning\"\n            });\n        }\n    };\n    return modal;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/advanced/ModalSystem.tsx\n");

/***/ }),

/***/ "(ssr)/./components/advanced/ToastSystem.tsx":
/*!*********************************************!*\
  !*** ./components/advanced/ToastSystem.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastContainer: () => (/* binding */ ToastContainer),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/stores/appStore */ \"(ssr)/./lib/stores/appStore.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastContainer,useToast auto */ \n\n\n\n\nconst Toast = ({ id, type, message, title, duration = 5000, action, closable = true, onClose })=>{\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLeaving, setIsLeaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 入场动画\n        const timer = setTimeout(()=>setIsVisible(true), 10);\n        return ()=>clearTimeout(timer);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 自动关闭\n        if (duration > 0) {\n            const timer = setTimeout(()=>{\n                handleClose();\n            }, duration);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        duration\n    ]);\n    const handleClose = ()=>{\n        setIsLeaving(true);\n        setTimeout(()=>{\n            onClose(id);\n        }, 300); // 等待退场动画完成\n    };\n    const getIcon = ()=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 16\n                }, undefined);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 16\n                }, undefined);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 16\n                }, undefined);\n            case \"info\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getBackgroundColor = ()=>{\n        switch(type){\n            case \"success\":\n                return \"bg-green-50 border-green-200\";\n            case \"error\":\n                return \"bg-red-50 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-50 border-yellow-200\";\n            case \"info\":\n                return \"bg-blue-50 border-blue-200\";\n            default:\n                return \"bg-gray-50 border-gray-200\";\n        }\n    };\n    const getTextColor = ()=>{\n        switch(type){\n            case \"success\":\n                return \"text-green-800\";\n            case \"error\":\n                return \"text-red-800\";\n            case \"warning\":\n                return \"text-yellow-800\";\n            case \"info\":\n                return \"text-blue-800\";\n            default:\n                return \"text-gray-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        transform transition-all duration-300 ease-in-out\n        ${isVisible && !isLeaving ? \"translate-x-0 opacity-100\" : \"translate-x-full opacity-0\"}\n        max-w-sm w-full ${getBackgroundColor()} border rounded-lg shadow-lg p-4 mb-3\n      `,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 mt-0.5\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: `text-sm font-semibold ${getTextColor()} mb-1`,\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-sm ${getTextColor()}`,\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, undefined),\n                            action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: action.onClick,\n                                className: `\n                mt-2 text-xs font-medium underline hover:no-underline\n                ${type === \"success\" ? \"text-green-700 hover:text-green-800\" : \"\"}\n                ${type === \"error\" ? \"text-red-700 hover:text-red-800\" : \"\"}\n                ${type === \"warning\" ? \"text-yellow-700 hover:text-yellow-800\" : \"\"}\n                ${type === \"info\" ? \"text-blue-700 hover:text-blue-800\" : \"\"}\n              `,\n                                children: action.label\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined),\n                    closable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleClose,\n                        className: `\n              flex-shrink-0 p-1 rounded-md hover:bg-white/50 transition-colors\n              ${getTextColor()}\n            `,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, undefined),\n            duration > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 w-full bg-white/30 rounded-full h-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `\n              h-1 rounded-full transition-all ease-linear\n              ${type === \"success\" ? \"bg-green-500\" : \"\"}\n              ${type === \"error\" ? \"bg-red-500\" : \"\"}\n              ${type === \"warning\" ? \"bg-yellow-500\" : \"\"}\n              ${type === \"info\" ? \"bg-blue-500\" : \"\"}\n            `,\n                    style: {\n                        width: \"100%\",\n                        animation: `shrink ${duration}ms linear`\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, undefined);\n};\n// Toast容器组件\nconst ToastContainer = ()=>{\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toasts = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useToasts)();\n    const removeToast = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.removeToast);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    if (!mounted) return null;\n    const container = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                id: toast.id,\n                type: toast.type,\n                message: toast.message,\n                duration: toast.duration,\n                onClose: removeToast\n            }, toast.id, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                lineNumber: 213,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(container, document.body);\n};\n// Toast Hook\nconst useToast = ()=>{\n    const addToast = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.addToast);\n    const removeToast = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.removeToast);\n    const clearToasts = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.clearToasts);\n    const toast = {\n        success: (message, options)=>{\n            return addToast({\n                type: \"success\",\n                message,\n                ...options\n            });\n        },\n        error: (message, options)=>{\n            return addToast({\n                type: \"error\",\n                message,\n                duration: 0,\n                ...options\n            });\n        },\n        warning: (message, options)=>{\n            return addToast({\n                type: \"warning\",\n                message,\n                ...options\n            });\n        },\n        info: (message, options)=>{\n            return addToast({\n                type: \"info\",\n                message,\n                ...options\n            });\n        },\n        custom: (config)=>{\n            return addToast(config);\n        },\n        dismiss: (id)=>{\n            removeToast(id);\n        },\n        dismissAll: ()=>{\n            clearToasts();\n        }\n    };\n    return toast;\n};\n// 添加CSS动画\nconst toastStyles = `\n  @keyframes shrink {\n    from {\n      width: 100%;\n    }\n    to {\n      width: 0%;\n    }\n  }\n`;\n// 注入样式\nif (typeof document !== \"undefined\") {\n    const styleElement = document.createElement(\"style\");\n    styleElement.textContent = toastStyles;\n    document.head.appendChild(styleElement);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/advanced/ToastSystem.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/performance/monitor.ts":
/*!************************************!*\
  !*** ./lib/performance/monitor.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   performanceMonitor: () => (/* binding */ performanceMonitor),\n/* harmony export */   usePerformanceMonitor: () => (/* binding */ usePerformanceMonitor)\n/* harmony export */ });\n/* harmony import */ var _stores_appStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stores/appStore */ \"(ssr)/./lib/stores/appStore.ts\");\n/* __next_internal_client_entry_do_not_use__ performanceMonitor,usePerformanceMonitor auto */ \n// 性能监控类\nclass PerformanceMonitor {\n    constructor(){\n        this.metrics = [];\n        this.observers = [];\n        this.isEnabled = true;\n        if (false) {}\n    }\n    // 初始化性能观察器\n    initializeObservers() {\n        // 监控导航性能\n        if (\"PerformanceObserver\" in window) {\n            const navObserver = new PerformanceObserver((list)=>{\n                for (const entry of list.getEntries()){\n                    if (entry.entryType === \"navigation\") {\n                        this.recordPageLoad(entry);\n                    }\n                }\n            });\n            navObserver.observe({\n                entryTypes: [\n                    \"navigation\"\n                ]\n            });\n            this.observers.push(navObserver);\n            // 监控资源加载\n            const resourceObserver = new PerformanceObserver((list)=>{\n                for (const entry of list.getEntries()){\n                    if (entry.entryType === \"resource\") {\n                        this.recordResourceLoad(entry);\n                    }\n                }\n            });\n            resourceObserver.observe({\n                entryTypes: [\n                    \"resource\"\n                ]\n            });\n            this.observers.push(resourceObserver);\n            // 监控用户交互\n            const interactionObserver = new PerformanceObserver((list)=>{\n                for (const entry of list.getEntries()){\n                    if (entry.entryType === \"event\") {\n                        this.recordInteraction(entry);\n                    }\n                }\n            });\n            try {\n                interactionObserver.observe({\n                    entryTypes: [\n                        \"event\"\n                    ]\n                });\n                this.observers.push(interactionObserver);\n            } catch (e) {\n                // Event timing API not supported\n                console.warn(\"Event timing API not supported\");\n            }\n        }\n    }\n    // 设置错误处理\n    setupErrorHandling() {\n        // JavaScript错误\n        window.addEventListener(\"error\", (event)=>{\n            this.recordError({\n                type: \"javascript\",\n                message: event.message,\n                stack: event.error?.stack,\n                timestamp: Date.now(),\n                route: window.location.pathname,\n                userAgent: navigator.userAgent\n            });\n        });\n        // Promise拒绝错误\n        window.addEventListener(\"unhandledrejection\", (event)=>{\n            this.recordError({\n                type: \"javascript\",\n                message: event.reason?.message || \"Unhandled Promise Rejection\",\n                stack: event.reason?.stack,\n                timestamp: Date.now(),\n                route: window.location.pathname,\n                userAgent: navigator.userAgent\n            });\n        });\n    }\n    // 记录页面加载性能\n    recordPageLoad(entry) {\n        const metric = {\n            startTime: entry.startTime,\n            endTime: entry.loadEventEnd,\n            duration: entry.loadEventEnd - entry.startTime,\n            route: window.location.pathname\n        };\n        this.addMetric({\n            pageLoad: metric\n        });\n        // 更新应用状态\n        _stores_appStore__WEBPACK_IMPORTED_MODULE_0__.useAppStore.getState().recordPageLoadTime(metric.duration);\n    }\n    // 记录资源加载性能\n    recordResourceLoad(entry) {\n        // 只记录API调用\n        if (entry.name.includes(\"/api/\")) {\n            const metric = {\n                endpoint: entry.name,\n                method: \"GET\",\n                startTime: entry.startTime,\n                endTime: entry.responseEnd,\n                duration: entry.responseEnd - entry.startTime,\n                status: 200,\n                size: entry.transferSize\n            };\n            this.addMetric({\n                network: metric\n            });\n        }\n    }\n    // 记录用户交互\n    recordInteraction(entry) {\n        const metric = {\n            type: entry.name,\n            element: entry.target?.tagName || \"unknown\",\n            timestamp: entry.startTime,\n            duration: entry.duration\n        };\n        this.addMetric({\n            interaction: metric\n        });\n    }\n    // 记录错误\n    recordError(error) {\n        this.addMetric({\n            error\n        });\n        _stores_appStore__WEBPACK_IMPORTED_MODULE_0__.useAppStore.getState().incrementErrorCount();\n    }\n    // 添加指标\n    addMetric(metric) {\n        if (!this.isEnabled) return;\n        this.metrics.push(metric);\n        // 限制指标数量，避免内存泄漏\n        if (this.metrics.length > 1000) {\n            this.metrics = this.metrics.slice(-500);\n        }\n    }\n    // 公共API：记录API调用\n    recordApiCall(endpoint, method, startTime, endTime, status, size) {\n        const metric = {\n            endpoint,\n            method,\n            startTime,\n            endTime,\n            duration: endTime - startTime,\n            status,\n            size\n        };\n        this.addMetric({\n            network: metric\n        });\n        _stores_appStore__WEBPACK_IMPORTED_MODULE_0__.useAppStore.getState().recordApiResponseTime(endpoint, metric.duration);\n    }\n    // 公共API：记录自定义交互\n    recordCustomInteraction(type, element, duration) {\n        const metric = {\n            type,\n            element,\n            timestamp: Date.now(),\n            duration\n        };\n        this.addMetric({\n            interaction: metric\n        });\n    }\n    // 获取性能报告\n    getPerformanceReport() {\n        const now = Date.now();\n        const last24Hours = now - 24 * 60 * 60 * 1000;\n        const recentMetrics = this.metrics.filter((metric)=>{\n            const timestamp = metric.pageLoad?.startTime || metric.network?.startTime || metric.interaction?.timestamp || metric.error?.timestamp || 0;\n            return timestamp > last24Hours;\n        });\n        return {\n            totalMetrics: recentMetrics.length,\n            pageLoads: recentMetrics.filter((m)=>m.pageLoad).length,\n            apiCalls: recentMetrics.filter((m)=>m.network).length,\n            interactions: recentMetrics.filter((m)=>m.interaction).length,\n            errors: recentMetrics.filter((m)=>m.error).length,\n            averagePageLoadTime: this.calculateAveragePageLoadTime(recentMetrics),\n            averageApiResponseTime: this.calculateAverageApiResponseTime(recentMetrics),\n            errorRate: this.calculateErrorRate(recentMetrics)\n        };\n    }\n    // 计算平均页面加载时间\n    calculateAveragePageLoadTime(metrics) {\n        const pageLoads = metrics.filter((m)=>m.pageLoad);\n        if (pageLoads.length === 0) return 0;\n        const total = pageLoads.reduce((sum, m)=>sum + (m.pageLoad?.duration || 0), 0);\n        return total / pageLoads.length;\n    }\n    // 计算平均API响应时间\n    calculateAverageApiResponseTime(metrics) {\n        const apiCalls = metrics.filter((m)=>m.network);\n        if (apiCalls.length === 0) return 0;\n        const total = apiCalls.reduce((sum, m)=>sum + (m.network?.duration || 0), 0);\n        return total / apiCalls.length;\n    }\n    // 计算错误率\n    calculateErrorRate(metrics) {\n        const errors = metrics.filter((m)=>m.error).length;\n        const total = metrics.length;\n        return total > 0 ? errors / total * 100 : 0;\n    }\n    // 启用/禁用监控\n    setEnabled(enabled) {\n        this.isEnabled = enabled;\n    }\n    // 清理资源\n    cleanup() {\n        this.observers.forEach((observer)=>observer.disconnect());\n        this.observers = [];\n        this.metrics = [];\n    }\n}\n// 创建全局实例\nconst performanceMonitor = new PerformanceMonitor();\n// React Hook\nconst usePerformanceMonitor = ()=>{\n    return {\n        recordApiCall: performanceMonitor.recordApiCall.bind(performanceMonitor),\n        recordInteraction: performanceMonitor.recordCustomInteraction.bind(performanceMonitor),\n        getReport: performanceMonitor.getPerformanceReport.bind(performanceMonitor),\n        setEnabled: performanceMonitor.setEnabled.bind(performanceMonitor)\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/performance/monitor.ts\n");

/***/ }),

/***/ "(ssr)/./lib/stores/appStore.ts":
/*!********************************!*\
  !*** ./lib/stores/appStore.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppData: () => (/* binding */ useAppData),\n/* harmony export */   useAppPerformance: () => (/* binding */ useAppPerformance),\n/* harmony export */   useAppPreferences: () => (/* binding */ useAppPreferences),\n/* harmony export */   useAppStore: () => (/* binding */ useAppStore),\n/* harmony export */   useAppUI: () => (/* binding */ useAppUI),\n/* harmony export */   useError: () => (/* binding */ useError),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage),\n/* harmony export */   useLoading: () => (/* binding */ useLoading),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useToasts: () => (/* binding */ useToasts)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var zustand_middleware_immer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware/immer */ \"(ssr)/./node_modules/zustand/esm/middleware/immer.mjs\");\n/* __next_internal_client_entry_do_not_use__ useAppStore,useAppPreferences,useAppUI,useAppData,useAppPerformance,useTheme,useLanguage,useLoading,useError,useToasts auto */ \n\n\n// 默认偏好设置\nconst defaultPreferences = {\n    theme: \"system\",\n    language: \"zh\",\n    fontSize: \"medium\",\n    animations: true,\n    notifications: {\n        browser: true,\n        email: false,\n        sms: false\n    },\n    privacy: {\n        analytics: true,\n        cookies: true,\n        dataSharing: false\n    },\n    accessibility: {\n        highContrast: false,\n        reducedMotion: false,\n        screenReader: false\n    }\n};\n// 默认应用状态\nconst defaultState = {\n    preferences: defaultPreferences,\n    ui: {\n        sidebarOpen: false,\n        loading: false,\n        error: null,\n        modal: {\n            isOpen: false,\n            type: null,\n            data: null\n        },\n        toast: []\n    },\n    data: {\n        lastSync: null,\n        version: \"1.0.0\",\n        buildTime: new Date().toISOString()\n    },\n    performance: {\n        pageLoadTime: 0,\n        apiResponseTimes: {},\n        errorCount: 0\n    }\n};\n// 生成唯一ID\nconst generateId = ()=>`${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n// 创建应用Store\nconst useAppStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((0,zustand_middleware_immer__WEBPACK_IMPORTED_MODULE_2__.immer)((set, get)=>({\n        ...defaultState,\n        // 偏好设置Actions\n        updatePreferences: (newPreferences)=>{\n            set((state)=>{\n                Object.assign(state.preferences, newPreferences);\n            });\n        },\n        resetPreferences: ()=>{\n            set((state)=>{\n                state.preferences = defaultPreferences;\n            });\n        },\n        // UI控制Actions\n        setSidebarOpen: (open)=>{\n            set((state)=>{\n                state.ui.sidebarOpen = open;\n            });\n        },\n        toggleSidebar: ()=>{\n            set((state)=>{\n                state.ui.sidebarOpen = !state.ui.sidebarOpen;\n            });\n        },\n        setLoading: (loading)=>{\n            set((state)=>{\n                state.ui.loading = loading;\n            });\n        },\n        setError: (error)=>{\n            set((state)=>{\n                state.ui.error = error;\n            });\n        },\n        // 模态框控制Actions\n        openModal: (type, data = null)=>{\n            set((state)=>{\n                state.ui.modal = {\n                    isOpen: true,\n                    type,\n                    data\n                };\n            });\n        },\n        closeModal: ()=>{\n            set((state)=>{\n                state.ui.modal = {\n                    isOpen: false,\n                    type: null,\n                    data: null\n                };\n            });\n        },\n        // Toast通知Actions\n        addToast: (toast)=>{\n            const id = generateId();\n            set((state)=>{\n                state.ui.toast.push({\n                    ...toast,\n                    id\n                });\n            });\n            // 自动移除Toast\n            if (toast.duration !== 0) {\n                setTimeout(()=>{\n                    get().removeToast(id);\n                }, toast.duration || 5000);\n            }\n            return id;\n        },\n        removeToast: (id)=>{\n            set((state)=>{\n                state.ui.toast = state.ui.toast.filter((t)=>t.id !== id);\n            });\n        },\n        clearToasts: ()=>{\n            set((state)=>{\n                state.ui.toast = [];\n            });\n        },\n        // 数据同步Actions\n        updateLastSync: ()=>{\n            set((state)=>{\n                state.data.lastSync = new Date().toISOString();\n            });\n        },\n        // 性能监控Actions\n        recordPageLoadTime: (time)=>{\n            set((state)=>{\n                state.performance.pageLoadTime = time;\n            });\n        },\n        recordApiResponseTime: (endpoint, time)=>{\n            set((state)=>{\n                state.performance.apiResponseTimes[endpoint] = time;\n            });\n        },\n        incrementErrorCount: ()=>{\n            set((state)=>{\n                state.performance.errorCount += 1;\n            });\n        },\n        resetPerformanceMetrics: ()=>{\n            set((state)=>{\n                state.performance = {\n                    pageLoadTime: 0,\n                    apiResponseTimes: {},\n                    errorCount: 0\n                };\n            });\n        }\n    })), {\n    name: \"periodhub-app-store\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.createJSONStorage)(()=>localStorage),\n    partialize: (state)=>({\n            preferences: state.preferences,\n            data: {\n                lastSync: state.data.lastSync,\n                version: state.data.version\n            }\n        })\n}), {\n    name: \"PeriodHub App Store\"\n}));\n// 选择器Hooks\nconst useAppPreferences = ()=>useAppStore((state)=>state.preferences);\nconst useAppUI = ()=>useAppStore((state)=>state.ui);\nconst useAppData = ()=>useAppStore((state)=>state.data);\nconst useAppPerformance = ()=>useAppStore((state)=>state.performance);\n// 便捷Hooks\nconst useTheme = ()=>useAppStore((state)=>state.preferences.theme);\nconst useLanguage = ()=>useAppStore((state)=>state.preferences.language);\nconst useLoading = ()=>useAppStore((state)=>state.ui.loading);\nconst useError = ()=>useAppStore((state)=>state.ui.error);\nconst useToasts = ()=>useAppStore((state)=>state.ui.toast);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/stores/appStore.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7d6af9bbd4ce\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZXJpb2RodWItaGVhbHRoLy4vYXBwL2dsb2JhbHMuY3NzPzg2OWQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3ZDZhZjliYmQ0Y2VcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/interactive-tools/page.tsx":
/*!*************************************************!*\
  !*** ./app/[locale]/interactive-tools/page.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InteractiveToolsPage),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/RequestLocaleCache.js\");\n/* harmony import */ var _components_ImagePlaceholder__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ImagePlaceholder */ \"(rsc)/./components/ImagePlaceholder.tsx\");\n/* harmony import */ var _components_BreathingExercise__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/BreathingExercise */ \"(rsc)/./components/BreathingExercise.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardCheck_Lightbulb_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardCheck,Lightbulb,Search,User!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardCheck_Lightbulb_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardCheck,Lightbulb,Search,User!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardCheck_Lightbulb_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardCheck,Lightbulb,Search,User!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardCheck_Lightbulb_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardCheck,Lightbulb,Search,User!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ClipboardCheck_Lightbulb_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ClipboardCheck,Lightbulb,Search,User!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/i18n */ \"(rsc)/./i18n.ts\");\n\n\n\n\n\n // Icons for cards\n\n// Generate metadata for the page\nasync function generateMetadata({ params: { locale } }) {\n    const t = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n        locale,\n        namespace: \"interactiveToolsPage\"\n    });\n    return {\n        title: t(\"title\"),\n        description: t(\"description\")\n    };\n}\n// Generate static params for all supported locales\nasync function generateStaticParams() {\n    return _i18n__WEBPACK_IMPORTED_MODULE_4__.locales.map((locale)=>({\n            locale\n        }));\n}\nasync function InteractiveToolsPage({ params: { locale } }) {\n    // Enable static rendering\n    (0,next_intl_server__WEBPACK_IMPORTED_MODULE_6__.setCachedRequestLocale)(locale);\n    const t = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n        locale,\n        namespace: \"interactiveToolsPage\"\n    });\n    const commonT = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n        locale,\n        namespace: \"common\"\n    });\n    const tools = [\n        {\n            title: t(\"symptomAssessment.title\"),\n            description: t(\"symptomAssessment.description\"),\n            href: `/${locale}/interactive-tools/symptom-assessment`,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ClipboardCheck_Lightbulb_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"w-8 h-8 text-primary-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                lineNumber: 44,\n                columnNumber: 13\n            }, this),\n            cta: t(\"symptomAssessment.startButton\")\n        },\n        {\n            title: t(\"periodPainAssessment.title\"),\n            description: t(\"periodPainAssessment.description\"),\n            href: `/${locale}/interactive-tools/period-pain-assessment`,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ClipboardCheck_Lightbulb_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"w-8 h-8 text-pink-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                lineNumber: 51,\n                columnNumber: 13\n            }, this),\n            cta: t(\"periodPainAssessment.cta\")\n        },\n        {\n            title: t(\"painTracker.title\"),\n            description: t(\"painTracker.description\"),\n            href: `/${locale}/interactive-tools/pain-tracker`,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ClipboardCheck_Lightbulb_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"w-8 h-8 text-secondary-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                lineNumber: 58,\n                columnNumber: 13\n            }, this),\n            cta: t(\"painTracker.startButton\")\n        },\n        {\n            title: t(\"constitutionTest.title\"),\n            description: t(\"constitutionTest.description\"),\n            href: `/${locale}/interactive-tools/constitution-test`,\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ClipboardCheck_Lightbulb_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"w-8 h-8 text-green-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                lineNumber: 65,\n                columnNumber: 13\n            }, this),\n            cta: t(\"constitutionTest.cta\")\n        },\n        {\n            title: t(\"personalizedInsights.title\"),\n            description: t(\"personalizedInsights.description\"),\n            href: \"#\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ClipboardCheck_Lightbulb_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"w-8 h-8 text-accent-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                lineNumber: 72,\n                columnNumber: 13\n            }, this),\n            cta: commonT(\"comingSoon\")\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 sm:space-y-12 mobile-safe-area\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"text-center px-4 sm:px-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-primary-700 mb-3 sm:mb-4 leading-tight\",\n                        children: t(\"title\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-base sm:text-lg text-neutral-600 max-w-3xl mx-auto leading-relaxed\",\n                        children: t(\"description\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-gradient-to-br from-primary-50 to-neutral-50 p-4 sm:p-6 md:p-8 rounded-xl mx-4 sm:mx-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 gap-6 sm:gap-8 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base text-neutral-700 leading-relaxed\",\n                                    children: t(\"toolsIntroduction\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center order-first md:order-last\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImagePlaceholder__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    filename: \"assessment-illustration.jpg\",\n                                    alt: \"Woman using digital health assessment tool on tablet in comfortable home setting\",\n                                    width: 300,\n                                    height: 225,\n                                    description: \"Woman using digital health assessment tool, modern tablet interface, comfortable home setting, soft lighting\",\n                                    className: \"w-full max-w-sm sm:max-w-md\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"container-custom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 max-w-6xl mx-auto\",\n                    children: tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card flex flex-col items-center text-center h-full p-4 sm:p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center rounded-full bg-neutral-100 mb-4 sm:mb-6\",\n                                    children: tool.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-base sm:text-lg lg:text-xl font-semibold text-neutral-800 mb-2 sm:mb-3 leading-tight\",\n                                    children: tool.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base text-neutral-600 mb-4 sm:mb-6 flex-grow leading-relaxed\",\n                                    children: tool.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                tool.href === \"#\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"btn-disabled w-full mobile-touch-target text-sm sm:text-base px-4 py-3\",\n                                    children: tool.cta\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 18\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: tool.href,\n                                    className: `w-full mobile-touch-target text-sm sm:text-base px-4 py-3 text-center ${tool.title.includes(\"Symptom\") || tool.title.includes(\"症状\") ? \"btn-primary\" : \"btn-secondary\"}`,\n                                    children: tool.cta\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, tool.title, true, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"breathing-exercise\",\n                className: \"container-custom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 sm:space-y-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-4 sm:p-6 lg:p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-blue-100 rounded-full mb-4 sm:mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl sm:text-3xl\",\n                                        children: \"\\uD83E\\uDEC1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl sm:text-2xl md:text-3xl font-bold text-blue-700 mb-3 sm:mb-4 leading-tight\",\n                                    children: t(\"breathingExercise.title\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm sm:text-base lg:text-lg text-neutral-600 max-w-3xl mx-auto leading-relaxed\",\n                                    children: t(\"breathingExercise.description\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BreathingExercise__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                locale: locale\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 rounded-lg p-6 max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-blue-800 mb-3\",\n                                    children: t(\"breathingExercise.usageTips.title\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 gap-4 text-sm text-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: t(\"breathingExercise.usageTips.bestTiming.title\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: t.raw(\"breathingExercise.usageTips.bestTiming.items\").map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                item\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: t(\"breathingExercise.usageTips.precautions.title\")\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-1\",\n                                                    children: t.raw(\"breathingExercise.usageTips.precautions.items\").map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: [\n                                                                \"• \",\n                                                                item\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-neutral-700\",\n                    children: t(\"developmentNote\")\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border-l-4 border-red-500 text-red-700 p-4 my-8 rounded-r-lg\",\n                role: \"alert\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-bold\",\n                        children: commonT(\"importantNote\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: commonT(\"medicalDisclaimer\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/interactive-tools/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/layout.tsx":
/*!*********************************!*\
  !*** ./app/[locale]/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getTranslations.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n */ \"(rsc)/./i18n.ts\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\n\n\n\n// Generate metadata\nasync function generateMetadata({ params: { locale } }) {\n    const t = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({\n        locale,\n        namespace: \"site\"\n    });\n    return {\n        title: {\n            template: \"%s | periodhub.health\",\n            default: t(\"title\")\n        },\n        description: t(\"description\"),\n        keywords: [\n            \"period pain relief\",\n            \"menstrual cramps\",\n            \"natural remedies\",\n            \"period management\",\n            \"women's health\",\n            \"dysmenorrhea treatment\",\n            \"periodhub.health\",\n            \"menstrual health\",\n            \"articles\",\n            \"therapies\"\n        ],\n        authors: [\n            {\n                name: \"periodhub.health team\"\n            }\n        ],\n        creator: \"periodhub.health\",\n        publisher: \"periodhub.health\",\n        formatDetection: {\n            email: false,\n            address: false,\n            telephone: false\n        },\n        metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || \"https://periodhub.health\"),\n        alternates: {\n            canonical: \"/\",\n            languages: {\n                \"en-US\": \"/en\",\n                \"zh-CN\": \"/zh\"\n            }\n        },\n        openGraph: {\n            type: \"website\",\n            locale: \"en_US\",\n            url: \"/\",\n            title: t(\"title\"),\n            description: t(\"description\"),\n            siteName: t(\"name\")\n        },\n        twitter: {\n            card: \"summary_large_image\",\n            title: t(\"title\"),\n            description: t(\"description\")\n        },\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                \"max-video-preview\": -1,\n                \"max-image-preview\": \"large\",\n                \"max-snippet\": -1\n            }\n        }\n    };\n}\n// Generate static params for all supported locales\nfunction generateStaticParams() {\n    return _i18n__WEBPACK_IMPORTED_MODULE_2__.locales.map((locale)=>({\n            locale\n        }));\n}\nasync function LocaleLayout({ children, params: { locale } }) {\n    // Validate the requested locale\n    if (!_i18n__WEBPACK_IMPORTED_MODULE_2__.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    // Get messages for the requested locale\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        locale: locale,\n        messages: messages,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-grow container-custom py-8\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/icon.tsx?__next_metadata_image_meta__":
/*!***************************************************!*\
  !*** ./app/icon.tsx?__next_metadata_image_meta__ ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contentType: () => (/* binding */ contentType),\n/* harmony export */   \"default\": () => (/* binding */ Icon),\n/* harmony export */   runtime: () => (/* binding */ runtime),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_og__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/og */ \"(rsc)/./node_modules/next/dist/api/og.js\");\n\n\n// Route segment config\nconst runtime = \"edge\";\n// Image metadata\nconst size = {\n    width: 32,\n    height: 32\n};\nconst contentType = \"image/png\";\n// Image generation\nfunction Icon() {\n    return new next_og__WEBPACK_IMPORTED_MODULE_1__.ImageResponse(// ImageResponse JSX element\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            fontSize: 24,\n            background: \"#ec5387\",\n            width: \"100%\",\n            height: \"100%\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            color: \"white\",\n            borderRadius: \"50%\"\n        },\n        children: \"P\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/icon.tsx\",\n        lineNumber: 18,\n        columnNumber: 7\n    }, this), // ImageResponse options\n    {\n        // For convenience, we can re-use the exported icons size metadata\n        // config to also set the ImageResponse's width and height.\n        ...size\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/icon.tsx?__next_metadata_image_meta__\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_advanced_AppProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/advanced/AppProvider */ \"(rsc)/./components/advanced/AppProvider.tsx\");\n\n\n\n\n\n// Generate metadata\nconst metadata = {\n    title: {\n        template: \"%s | periodhub.health\",\n        default: \"periodhub.health - Your Guide to Menstrual Wellness\"\n    },\n    description: \"Your compassionate guide to navigating menstrual pain with effective solutions, supportive resources, and a path to better menstrual health.\",\n    keywords: [\n        \"period pain relief\",\n        \"menstrual cramps\",\n        \"natural remedies\",\n        \"period management\",\n        \"women's health\",\n        \"dysmenorrhea treatment\",\n        \"periodhub.health\",\n        \"menstrual health\",\n        \"articles\",\n        \"therapies\"\n    ],\n    authors: [\n        {\n            name: \"periodhub.health team\"\n        }\n    ],\n    creator: \"periodhub.health\",\n    publisher: \"periodhub.health\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || \"https://periodhub.health\"),\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    // Get the current locale from headers or default to 'en'\n    const headersList = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n    const pathname = headersList.get(\"x-pathname\") || \"\";\n    const locale = pathname.startsWith(\"/zh\") ? \"zh\" : \"en\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        className: `scroll-smooth ${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#ffffff\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=5\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://v3.fal.media\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://unpkg.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased bg-neutral-50 text-neutral-900 flex flex-col min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_advanced_AppProvider__WEBPACK_IMPORTED_MODULE_3__.AppProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sr-only\",\n                        children: \"This website provides information about menstrual health for educational purposes only. The content is not intended to be a substitute for professional medical advice, diagnosis, or treatment. Always seek the advice of your physician or other qualified health provider with any questions you may have.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/BreathingExercise.tsx":
/*!******************************************!*\
  !*** ./components/BreathingExercise.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx#default`));


/***/ }),

/***/ "(rsc)/./components/ImagePlaceholder.tsx":
/*!*****************************************!*\
  !*** ./components/ImagePlaceholder.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImagePlaceholder)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction ImagePlaceholder({ filename, alt, width = 400, height = 300, className = \"\", description }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `bg-gradient-to-br from-gray-100 to-gray-200 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-4 ${className}`,\n        style: {\n            width: width,\n            height: height,\n            minHeight: height\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-400 text-2xl mb-2\",\n                children: \"\\uD83D\\uDCF7\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/ImagePlaceholder.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm font-medium text-gray-600 mb-1\",\n                        children: filename\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/ImagePlaceholder.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 mb-2\",\n                        children: [\n                            width,\n                            \"x\",\n                            height,\n                            \"px\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/ImagePlaceholder.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400 max-w-xs\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/ImagePlaceholder.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-400 mt-2 italic\",\n                        children: [\n                            \"Alt: \",\n                            alt\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/ImagePlaceholder.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/ImagePlaceholder.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/ImagePlaceholder.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/ImagePlaceholder.tsx\n");

/***/ }),

/***/ "(rsc)/./components/advanced/AppProvider.tsx":
/*!*********************************************!*\
  !*** ./components/advanced/AppProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppProvider: () => (/* binding */ e0),
/* harmony export */   ComponentProvider: () => (/* binding */ e2),
/* harmony export */   PageProvider: () => (/* binding */ e1),
/* harmony export */   usePagePerformance: () => (/* binding */ e3),
/* harmony export */   useUserTracking: () => (/* binding */ e4)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#AppProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#PageProvider`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#ComponentProvider`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#usePagePerformance`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#useUserTracking`);


/***/ }),

/***/ "(rsc)/./i18n.ts":
/*!*****************!*\
  !*** ./i18n.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   locales: () => (/* binding */ locales)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n\n// Can be imported from a shared config\nconst locales = [\n    \"en\",\n    \"zh\"\n];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(async ({ requestLocale })=>{\n    // This typically corresponds to the `[locale]` segment\n    let locale = await requestLocale;\n    // Ensure that a valid locale is used\n    if (!locale || !locales.includes(locale)) {\n        locale = \"zh\";\n    }\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default,\n        timeZone: \"Asia/Shanghai\",\n        now: new Date(),\n        formats: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                }\n            },\n            number: {\n                precise: {\n                    maximumFractionDigits: 5\n                }\n            },\n            list: {\n                enumeration: {\n                    style: \"long\",\n                    type: \"conjunction\"\n                }\n            }\n        }\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./i18n.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/icon.tsx?__next_metadata__":
/*!****************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/icon.tsx?__next_metadata__ ***!
  \****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Users_duting_Downloads_periodhub_health_01_app_icon_tsx_next_metadata_image_meta___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/icon.tsx?__next_metadata_image_meta__ */ \"(rsc)/./app/icon.tsx?__next_metadata_image_meta__\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_1__);\n    \n    \n\n    const imageModule = {\n      runtime: _Users_duting_Downloads_periodhub_health_01_app_icon_tsx_next_metadata_image_meta___WEBPACK_IMPORTED_MODULE_0__.runtime,size: _Users_duting_Downloads_periodhub_health_01_app_icon_tsx_next_metadata_image_meta___WEBPACK_IMPORTED_MODULE_0__.size,contentType: _Users_duting_Downloads_periodhub_health_01_app_icon_tsx_next_metadata_image_meta___WEBPACK_IMPORTED_MODULE_0__.contentType\n    }\n\n    /* harmony default export */ async function __WEBPACK_DEFAULT_EXPORT__(props) {\n      const { __metadata_id__: _, ...params } = props.params\n      const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_1__.fillMetadataSegment)(\".\", params, \"icon\")\n\n      const { generateImageMetadata } = imageModule\n\n      function getImageMetadata(imageMetadata, idParam) {\n        const data = {\n          alt: imageMetadata.alt,\n          type: imageMetadata.contentType || 'image/png',\n          url: imageUrl + (idParam ? ('/' + idParam) : '') + \"?92af7df7fd37ba54\",\n        }\n        const { size } = imageMetadata\n        if (size) {\n          data.sizes = size.width + \"x\" + size.height;\n        }\n        return data\n      }\n\n      if (generateImageMetadata) {\n        const imageMetadataArray = await generateImageMetadata({ params })\n        return imageMetadataArray.map((imageMetadata, index) => {\n          const idParam = (imageMetadata.id || index) + ''\n          return getImageMetadata(imageMetadata, idParam)\n        })\n      } else {\n        return [getImageMetadata(imageModule, '')]\n      }\n    }//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/icon.tsx?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@formatjs","vendor-chunks/use-intl","vendor-chunks/intl-messageformat","vendor-chunks/immer","vendor-chunks/tslib","vendor-chunks/zustand","vendor-chunks/next-intl","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Finteractive-tools%2Fpage&page=%2F%5Blocale%5D%2Finteractive-tools%2Fpage&appPaths=%2F%5Blocale%5D%2Finteractive-tools%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Finteractive-tools%2Fpage.tsx&appDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();