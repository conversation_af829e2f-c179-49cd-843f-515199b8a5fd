/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2Fadvanced%2FAppProvider.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2Fadvanced%2FAppProvider.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/advanced/AppProvider.tsx */ \"(ssr)/./components/advanced/AppProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZHV0aW5nJTJGRG93bmxvYWRzJTJGcGVyaW9kaHViLWhlYWx0aF8lRTUlODklQUYlRTYlOUMlQUMwMSVFNyU4OSU4OCUyRmNvbXBvbmVudHMlMkZhZHZhbmNlZCUyRkFwcFByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkFwcFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZHV0aW5nJTJGRG93bmxvYWRzJTJGcGVyaW9kaHViLWhlYWx0aF8lRTUlODklQUYlRTYlOUMlQUMwMSVFNyU4OSU4OCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJkaXNwbGF5JTVDJTIyJTNBJTVDJTIyc3dhcCU1QyUyMiUyQyU1QyUyMnZhcmlhYmxlJTVDJTIyJTNBJTVDJTIyLS1mb250LWludGVyJTVDJTIyJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZkdXRpbmclMkZEb3dubG9hZHMlMkZwZXJpb2RodWItaGVhbHRoXyVFNSU4OSVBRiVFNiU5QyVBQzAxJUU3JTg5JTg4JTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUF3SiIsInNvdXJjZXMiOlsid2VicGFjazovL3BlcmlvZGh1Yi1oZWFsdGgvPzE1ZjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBcHBQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9kdXRpbmcvRG93bmxvYWRzL3BlcmlvZGh1Yi1oZWFsdGhf5Ymv5pysMDHniYgvY29tcG9uZW50cy9hZHZhbmNlZC9BcHBQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fcomponents%2Fadvanced%2FAppProvider.tsx%22%2C%22ids%22%3A%5B%22AppProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/advanced/AppProvider.tsx":
/*!*********************************************!*\
  !*** ./components/advanced/AppProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppProvider: () => (/* binding */ AppProvider),\n/* harmony export */   ComponentProvider: () => (/* binding */ ComponentProvider),\n/* harmony export */   PageProvider: () => (/* binding */ PageProvider),\n/* harmony export */   usePagePerformance: () => (/* binding */ usePagePerformance),\n/* harmony export */   useUserTracking: () => (/* binding */ useUserTracking)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ErrorBoundary */ \"(ssr)/./components/advanced/ErrorBoundary.tsx\");\n/* harmony import */ var _ToastSystem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ToastSystem */ \"(ssr)/./components/advanced/ToastSystem.tsx\");\n/* harmony import */ var _ModalSystem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ModalSystem */ \"(ssr)/./components/advanced/ModalSystem.tsx\");\n/* harmony import */ var _lib_stores_appStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/stores/appStore */ \"(ssr)/./lib/stores/appStore.ts\");\n/* harmony import */ var _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../lib/performance/monitor */ \"(ssr)/./lib/performance/monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ AppProvider,PageProvider,ComponentProvider,usePagePerformance,useUserTracking auto */ \n\n\n\n\n\n\n// 应用级别的Provider组件\nconst AppProvider = ({ children })=>{\n    const preferences = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_5__.useAppStore)((state)=>state.preferences);\n    const recordPageLoadTime = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_5__.useAppStore)((state)=>state.recordPageLoadTime);\n    // 初始化应用\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 记录页面加载时间\n        const loadTime = performance.now();\n        recordPageLoadTime(loadTime);\n        // 应用主题\n        applyTheme(preferences.theme);\n        // 应用字体大小\n        applyFontSize(preferences.fontSize);\n        // 应用可访问性设置\n        applyAccessibilitySettings(preferences.accessibility);\n        // 启用性能监控\n        if (preferences.privacy.analytics) {\n            _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__.performanceMonitor.setEnabled(true);\n        }\n        // 清理函数\n        return ()=>{\n            _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__.performanceMonitor.cleanup();\n        };\n    }, []);\n    // 监听偏好设置变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        applyTheme(preferences.theme);\n    }, [\n        preferences.theme\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        applyFontSize(preferences.fontSize);\n    }, [\n        preferences.fontSize\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        applyAccessibilitySettings(preferences.accessibility);\n    }, [\n        preferences.accessibility\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__.performanceMonitor.setEnabled(preferences.privacy.analytics);\n    }, [\n        preferences.privacy.analytics\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n        level: \"critical\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"app-container\",\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToastSystem__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModalSystem__WEBPACK_IMPORTED_MODULE_4__.ModalManager, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n// 应用主题\nfunction applyTheme(theme) {\n    const root = document.documentElement;\n    if (theme === \"system\") {\n        const prefersDark = window.matchMedia(\"(prefers-color-scheme: dark)\").matches;\n        theme = prefersDark ? \"dark\" : \"light\";\n    }\n    root.classList.remove(\"light\", \"dark\");\n    root.classList.add(theme);\n    // 更新meta标签\n    const metaTheme = document.querySelector('meta[name=\"theme-color\"]');\n    if (metaTheme) {\n        metaTheme.setAttribute(\"content\", theme === \"dark\" ? \"#1f2937\" : \"#ffffff\");\n    }\n}\n// 应用字体大小\nfunction applyFontSize(fontSize) {\n    const root = document.documentElement;\n    root.classList.remove(\"text-sm\", \"text-base\", \"text-lg\");\n    switch(fontSize){\n        case \"small\":\n            root.classList.add(\"text-sm\");\n            break;\n        case \"large\":\n            root.classList.add(\"text-lg\");\n            break;\n        default:\n            root.classList.add(\"text-base\");\n    }\n}\n// 应用可访问性设置\nfunction applyAccessibilitySettings(accessibility) {\n    const root = document.documentElement;\n    // 高对比度\n    if (accessibility.highContrast) {\n        root.classList.add(\"high-contrast\");\n    } else {\n        root.classList.remove(\"high-contrast\");\n    }\n    // 减少动画\n    if (accessibility.reducedMotion) {\n        root.classList.add(\"reduce-motion\");\n    } else {\n        root.classList.remove(\"reduce-motion\");\n    }\n    // 屏幕阅读器优化\n    if (accessibility.screenReader) {\n        root.classList.add(\"screen-reader-optimized\");\n    } else {\n        root.classList.remove(\"screen-reader-optimized\");\n    }\n}\nconst PageProvider = ({ children, title, description, keywords = [] })=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 更新页面标题\n        if (title) {\n            document.title = `${title} - Period Hub Health`;\n        }\n        // 更新meta描述\n        if (description) {\n            const metaDescription = document.querySelector('meta[name=\"description\"]');\n            if (metaDescription) {\n                metaDescription.setAttribute(\"content\", description);\n            }\n        }\n        // 更新meta关键词\n        if (keywords.length > 0) {\n            const metaKeywords = document.querySelector('meta[name=\"keywords\"]');\n            if (metaKeywords) {\n                metaKeywords.setAttribute(\"content\", keywords.join(\", \"));\n            }\n        }\n    }, [\n        title,\n        description,\n        keywords\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n        level: \"page\",\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, undefined);\n};\nconst ComponentProvider = ({ children, name, fallback })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n        level: \"component\",\n        fallback: fallback,\n        onError: (error, errorInfo)=>{\n            console.error(`Component error in ${name}:`, error, errorInfo);\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, undefined);\n};\n// 性能监控Hook\nconst usePagePerformance = (pageName)=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const startTime = performance.now();\n        // 记录页面访问\n        _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__.performanceMonitor.recordCustomInteraction(\"navigation\", pageName);\n        return ()=>{\n            const endTime = performance.now();\n            const duration = endTime - startTime;\n            // 记录页面停留时间\n            _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__.performanceMonitor.recordCustomInteraction(\"navigation\", `${pageName}_duration`, duration);\n        };\n    }, [\n        pageName\n    ]);\n};\n// 用户行为追踪Hook\nconst useUserTracking = ()=>{\n    const recordInteraction = (type, element, data)=>{\n        _lib_performance_monitor__WEBPACK_IMPORTED_MODULE_6__.performanceMonitor.recordCustomInteraction(type, element);\n        // 可以在这里添加更多的用户行为分析\n        console.log(\"User interaction:\", {\n            type,\n            element,\n            data,\n            timestamp: Date.now()\n        });\n    };\n    return {\n        recordInteraction\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/advanced/AppProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/advanced/ErrorBoundary.tsx":
/*!***********************************************!*\
  !*** ./components/advanced/ErrorBoundary.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   useErrorHandler: () => (/* binding */ useErrorHandler),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Home,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _lib_stores_appStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/stores/appStore */ \"(ssr)/./lib/stores/appStore.ts\");\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,withErrorBoundary,useErrorHandler auto */ \n\n\n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.retryCount = 0;\n        this.maxRetries = 3;\n        this.reportError = async (error, errorInfo)=>{\n            try {\n                // 这里可以发送到错误监控服务\n                const errorReport = {\n                    message: error.message,\n                    stack: error.stack,\n                    componentStack: errorInfo.componentStack,\n                    timestamp: new Date().toISOString(),\n                    url: window.location.href,\n                    userAgent: navigator.userAgent,\n                    errorId: this.state.errorId,\n                    level: this.props.level || \"component\"\n                };\n                // 发送到监控服务（示例）\n                // await fetch('/api/errors', {\n                //   method: 'POST',\n                //   headers: { 'Content-Type': 'application/json' },\n                //   body: JSON.stringify(errorReport),\n                // });\n                console.log(\"Error reported:\", errorReport);\n            } catch (reportingError) {\n                console.error(\"Failed to report error:\", reportingError);\n            }\n        };\n        this.handleRetry = ()=>{\n            if (this.retryCount < this.maxRetries) {\n                this.retryCount++;\n                this.setState({\n                    hasError: false,\n                    error: null,\n                    errorInfo: null,\n                    errorId: null\n                });\n            } else {\n                // 达到最大重试次数，刷新页面\n                window.location.reload();\n            }\n        };\n        this.handleGoHome = ()=>{\n            window.location.href = \"/\";\n        };\n        this.handleReportBug = ()=>{\n            const { error, errorInfo, errorId } = this.state;\n            const bugReport = {\n                errorId,\n                message: error?.message,\n                stack: error?.stack,\n                componentStack: errorInfo?.componentStack,\n                url: window.location.href,\n                timestamp: new Date().toISOString()\n            };\n            // 打开邮件客户端或错误报告页面\n            const subject = encodeURIComponent(`Bug Report: ${error?.message || \"Unknown Error\"}`);\n            const body = encodeURIComponent(`\nError ID: ${errorId}\nURL: ${window.location.href}\nTime: ${new Date().toISOString()}\n\nError Details:\n${error?.message}\n\nStack Trace:\n${error?.stack}\n\nComponent Stack:\n${errorInfo?.componentStack}\n\nPlease describe what you were doing when this error occurred:\n[Your description here]\n    `);\n            window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);\n        };\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null,\n            errorId: null\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error,\n            errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        this.setState({\n            errorInfo\n        });\n        // 记录错误到应用状态\n        _lib_stores_appStore__WEBPACK_IMPORTED_MODULE_2__.useAppStore.getState().incrementErrorCount();\n        // 调用自定义错误处理\n        if (this.props.onError) {\n            this.props.onError(error, errorInfo);\n        }\n        // 发送错误报告（在生产环境中）\n        if (false) {}\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            // 如果提供了自定义fallback，使用它\n            if (this.props.fallback) {\n                return this.props.fallback;\n            }\n            const { error, errorInfo, errorId } = this.state;\n            const { level = \"component\", showDetails = false } = this.props;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-[400px] flex items-center justify-center p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full bg-white rounded-lg shadow-lg border border-red-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-8 h-8 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: level === \"critical\" ? \"严重错误\" : \"出现错误\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: level === \"critical\" ? \"应用遇到了严重问题\" : \"这个组件遇到了问题\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-700 mb-2\",\n                                        children: \"我们很抱歉给您带来不便。错误已被记录，我们会尽快修复。\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this),\n                                    errorId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 font-mono bg-gray-50 p-2 rounded\",\n                                        children: [\n                                            \"错误ID: \",\n                                            errorId\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 19\n                                    }, this),\n                                    (showDetails || \"development\" === \"development\") && error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                        className: \"mt-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                                className: \"text-sm text-gray-600 cursor-pointer hover:text-gray-800\",\n                                                children: \"查看技术详情\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 p-3 bg-gray-50 rounded text-xs font-mono\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"错误信息:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-red-600\",\n                                                                children: error.message\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    error.stack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"堆栈跟踪:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                className: \"whitespace-pre-wrap text-gray-600 max-h-32 overflow-y-auto\",\n                                                                children: error.stack\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    errorInfo?.componentStack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"组件堆栈:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                                className: \"whitespace-pre-wrap text-gray-600 max-h-32 overflow-y-auto\",\n                                                                children: errorInfo.componentStack\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-2\",\n                                children: [\n                                    this.retryCount < this.maxRetries ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: this.handleRetry,\n                                        className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"重试 (\",\n                                                    this.maxRetries - this.retryCount,\n                                                    \" 次剩余)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.reload(),\n                                        className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"刷新页面\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: this.handleGoHome,\n                                                className: \"flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"返回首页\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: this.handleReportBug,\n                                                className: \"flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"报告问题\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n// 高阶组件包装器\nfunction withErrorBoundary(Component, errorBoundaryProps) {\n    const WrappedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n            ...errorBoundaryProps,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx\",\n            lineNumber: 276,\n            columnNumber: 5\n        }, this);\n    WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n    return WrappedComponent;\n}\n// Hook版本\nfunction useErrorHandler() {\n    const incrementErrorCount = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_2__.useAppStore)((state)=>state.incrementErrorCount);\n    return (error, errorInfo)=>{\n        incrementErrorCount();\n        console.error(\"Error caught by useErrorHandler:\", error, errorInfo);\n        // 可以在这里添加更多错误处理逻辑\n        if (false) {}\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/advanced/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./components/advanced/ModalSystem.tsx":
/*!*********************************************!*\
  !*** ./components/advanced/ModalSystem.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModalManager: () => (/* binding */ ModalManager),\n/* harmony export */   useModal: () => (/* binding */ useModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/stores/appStore */ \"(ssr)/./lib/stores/appStore.ts\");\n/* __next_internal_client_entry_do_not_use__ ModalManager,useModal auto */ \n\n\n\n\nconst Modal = ({ isOpen, onClose, type = \"default\", title, content, children, size = \"md\", closable = true, maskClosable = true, showFooter = false, confirmText = \"确认\", cancelText = \"取消\", onConfirm, onCancel, className = \"\", zIndex = 1000 })=>{\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            setIsVisible(true);\n            setIsAnimating(true);\n            document.body.style.overflow = \"hidden\";\n        } else {\n            setIsAnimating(false);\n            const timer = setTimeout(()=>{\n                setIsVisible(false);\n                document.body.style.overflow = \"\";\n            }, 200);\n            return ()=>clearTimeout(timer);\n        }\n        return ()=>{\n            document.body.style.overflow = \"\";\n        };\n    }, [\n        isOpen\n    ]);\n    const handleMaskClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.target === e.currentTarget && maskClosable) {\n            onClose();\n        }\n    }, [\n        maskClosable,\n        onClose\n    ]);\n    const handleConfirm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (onConfirm) {\n            try {\n                await onConfirm();\n                onClose();\n            } catch (error) {\n                console.error(\"Modal confirm error:\", error);\n            }\n        } else {\n            onClose();\n        }\n    }, [\n        onConfirm,\n        onClose\n    ]);\n    const handleCancel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (onCancel) {\n            onCancel();\n        }\n        onClose();\n    }, [\n        onCancel,\n        onClose\n    ]);\n    const getSizeClasses = ()=>{\n        switch(size){\n            case \"sm\":\n                return \"max-w-sm\";\n            case \"md\":\n                return \"max-w-md\";\n            case \"lg\":\n                return \"max-w-lg\";\n            case \"xl\":\n                return \"max-w-xl\";\n            case \"full\":\n                return \"max-w-full mx-4\";\n            default:\n                return \"max-w-md\";\n        }\n    };\n    const getTypeIcon = ()=>{\n        switch(type){\n            case \"confirm\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-6 h-6 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 16\n                }, undefined);\n            case \"alert\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-6 h-6 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    if (!isVisible) return null;\n    const modalContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed inset-0 flex items-center justify-center p-4`,\n        style: {\n            zIndex\n        },\n        onClick: handleMaskClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n          absolute inset-0 bg-black transition-opacity duration-200\n          ${isAnimating ? \"opacity-50\" : \"opacity-0\"}\n        `\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `\n          relative bg-white rounded-lg shadow-xl w-full ${getSizeClasses()}\n          transform transition-all duration-200\n          ${isAnimating ? \"scale-100 opacity-100\" : \"scale-95 opacity-0\"}\n          ${className}\n        `,\n                children: [\n                    (title || closable) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    getTypeIcon(),\n                                    title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined),\n                            closable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-1 text-gray-400 hover:text-gray-600 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: content || children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined),\n                    showFooter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end space-x-3 p-6 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCancel,\n                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors\",\n                                children: cancelText\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleConfirm,\n                                className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors\",\n                                children: confirmText\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(modalContent, document.body);\n};\n// 模态框管理器\nconst ModalManager = ()=>{\n    const modal = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.ui.modal);\n    const closeModal = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.closeModal);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Modal, {\n        isOpen: modal.isOpen,\n        onClose: closeModal,\n        ...modal.data\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, undefined);\n};\n// 模态框Hook\nconst useModal = ()=>{\n    const openModal = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.openModal);\n    const closeModal = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.closeModal);\n    const modal = {\n        open: (config)=>{\n            openModal(\"custom\", config);\n        },\n        close: ()=>{\n            closeModal();\n        },\n        confirm: (config)=>{\n            return new Promise((resolve)=>{\n                const modalConfig = {\n                    type: \"confirm\",\n                    title: config.title || \"确认操作\",\n                    content: config.content,\n                    showFooter: true,\n                    confirmText: config.confirmText || \"确认\",\n                    cancelText: config.cancelText || \"取消\",\n                    onConfirm: async ()=>{\n                        if (config.onConfirm) {\n                            await config.onConfirm();\n                        }\n                        resolve(true);\n                    },\n                    onCancel: ()=>{\n                        if (config.onCancel) {\n                            config.onCancel();\n                        }\n                        resolve(false);\n                    }\n                };\n                openModal(\"confirm\", modalConfig);\n            });\n        },\n        alert: (content, title)=>{\n            return new Promise((resolve)=>{\n                const modalConfig = {\n                    type: \"alert\",\n                    title: title || \"提示\",\n                    content,\n                    showFooter: true,\n                    confirmText: \"确定\",\n                    onConfirm: ()=>{\n                        resolve();\n                    }\n                };\n                openModal(\"alert\", modalConfig);\n            });\n        },\n        // 便捷方法\n        success: (content, title)=>{\n            return modal.alert(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"w-6 h-6 text-green-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: content\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, undefined), title || \"成功\");\n        },\n        error: (content, title)=>{\n            return modal.alert(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-6 h-6 text-red-500\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: content\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                lineNumber: 304,\n                columnNumber: 9\n            }, undefined), title || \"错误\");\n        },\n        warning: (content, title)=>{\n            return modal.confirm({\n                title: title || \"警告\",\n                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-6 h-6 text-yellow-500\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: content\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 11\n                }, undefined),\n                type: \"warning\"\n            });\n        }\n    };\n    return modal;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/advanced/ModalSystem.tsx\n");

/***/ }),

/***/ "(ssr)/./components/advanced/ToastSystem.tsx":
/*!*********************************************!*\
  !*** ./components/advanced/ToastSystem.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastContainer: () => (/* binding */ ToastContainer),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/stores/appStore */ \"(ssr)/./lib/stores/appStore.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastContainer,useToast auto */ \n\n\n\n\nconst Toast = ({ id, type, message, title, duration = 5000, action, closable = true, onClose })=>{\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLeaving, setIsLeaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 入场动画\n        const timer = setTimeout(()=>setIsVisible(true), 10);\n        return ()=>clearTimeout(timer);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 自动关闭\n        if (duration > 0) {\n            const timer = setTimeout(()=>{\n                handleClose();\n            }, duration);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        duration\n    ]);\n    const handleClose = ()=>{\n        setIsLeaving(true);\n        setTimeout(()=>{\n            onClose(id);\n        }, 300); // 等待退场动画完成\n    };\n    const getIcon = ()=>{\n        switch(type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 16\n                }, undefined);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 16\n                }, undefined);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 16\n                }, undefined);\n            case \"info\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getBackgroundColor = ()=>{\n        switch(type){\n            case \"success\":\n                return \"bg-green-50 border-green-200\";\n            case \"error\":\n                return \"bg-red-50 border-red-200\";\n            case \"warning\":\n                return \"bg-yellow-50 border-yellow-200\";\n            case \"info\":\n                return \"bg-blue-50 border-blue-200\";\n            default:\n                return \"bg-gray-50 border-gray-200\";\n        }\n    };\n    const getTextColor = ()=>{\n        switch(type){\n            case \"success\":\n                return \"text-green-800\";\n            case \"error\":\n                return \"text-red-800\";\n            case \"warning\":\n                return \"text-yellow-800\";\n            case \"info\":\n                return \"text-blue-800\";\n            default:\n                return \"text-gray-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        transform transition-all duration-300 ease-in-out\n        ${isVisible && !isLeaving ? \"translate-x-0 opacity-100\" : \"translate-x-full opacity-0\"}\n        max-w-sm w-full ${getBackgroundColor()} border rounded-lg shadow-lg p-4 mb-3\n      `,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 mt-0.5\",\n                        children: getIcon()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: `text-sm font-semibold ${getTextColor()} mb-1`,\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-sm ${getTextColor()}`,\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, undefined),\n                            action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: action.onClick,\n                                className: `\n                mt-2 text-xs font-medium underline hover:no-underline\n                ${type === \"success\" ? \"text-green-700 hover:text-green-800\" : \"\"}\n                ${type === \"error\" ? \"text-red-700 hover:text-red-800\" : \"\"}\n                ${type === \"warning\" ? \"text-yellow-700 hover:text-yellow-800\" : \"\"}\n                ${type === \"info\" ? \"text-blue-700 hover:text-blue-800\" : \"\"}\n              `,\n                                children: action.label\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined),\n                    closable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleClose,\n                        className: `\n              flex-shrink-0 p-1 rounded-md hover:bg-white/50 transition-colors\n              ${getTextColor()}\n            `,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, undefined),\n            duration > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-3 w-full bg-white/30 rounded-full h-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `\n              h-1 rounded-full transition-all ease-linear\n              ${type === \"success\" ? \"bg-green-500\" : \"\"}\n              ${type === \"error\" ? \"bg-red-500\" : \"\"}\n              ${type === \"warning\" ? \"bg-yellow-500\" : \"\"}\n              ${type === \"info\" ? \"bg-blue-500\" : \"\"}\n            `,\n                    style: {\n                        width: \"100%\",\n                        animation: `shrink ${duration}ms linear`\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, undefined);\n};\n// Toast容器组件\nconst ToastContainer = ()=>{\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toasts = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useToasts)();\n    const removeToast = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.removeToast);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    if (!mounted) return null;\n    const container = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                id: toast.id,\n                type: toast.type,\n                message: toast.message,\n                duration: toast.duration,\n                onClose: removeToast\n            }, toast.id, false, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n                lineNumber: 213,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx\",\n        lineNumber: 211,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(container, document.body);\n};\n// Toast Hook\nconst useToast = ()=>{\n    const addToast = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.addToast);\n    const removeToast = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.removeToast);\n    const clearToasts = (0,_lib_stores_appStore__WEBPACK_IMPORTED_MODULE_3__.useAppStore)((state)=>state.clearToasts);\n    const toast = {\n        success: (message, options)=>{\n            return addToast({\n                type: \"success\",\n                message,\n                ...options\n            });\n        },\n        error: (message, options)=>{\n            return addToast({\n                type: \"error\",\n                message,\n                duration: 0,\n                ...options\n            });\n        },\n        warning: (message, options)=>{\n            return addToast({\n                type: \"warning\",\n                message,\n                ...options\n            });\n        },\n        info: (message, options)=>{\n            return addToast({\n                type: \"info\",\n                message,\n                ...options\n            });\n        },\n        custom: (config)=>{\n            return addToast(config);\n        },\n        dismiss: (id)=>{\n            removeToast(id);\n        },\n        dismissAll: ()=>{\n            clearToasts();\n        }\n    };\n    return toast;\n};\n// 添加CSS动画\nconst toastStyles = `\n  @keyframes shrink {\n    from {\n      width: 100%;\n    }\n    to {\n      width: 0%;\n    }\n  }\n`;\n// 注入样式\nif (typeof document !== \"undefined\") {\n    const styleElement = document.createElement(\"style\");\n    styleElement.textContent = toastStyles;\n    document.head.appendChild(styleElement);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/advanced/ToastSystem.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/performance/monitor.ts":
/*!************************************!*\
  !*** ./lib/performance/monitor.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   performanceMonitor: () => (/* binding */ performanceMonitor),\n/* harmony export */   usePerformanceMonitor: () => (/* binding */ usePerformanceMonitor)\n/* harmony export */ });\n/* harmony import */ var _stores_appStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../stores/appStore */ \"(ssr)/./lib/stores/appStore.ts\");\n/* __next_internal_client_entry_do_not_use__ performanceMonitor,usePerformanceMonitor auto */ \n// 性能监控类\nclass PerformanceMonitor {\n    constructor(){\n        this.metrics = [];\n        this.observers = [];\n        this.isEnabled = true;\n        if (false) {}\n    }\n    // 初始化性能观察器\n    initializeObservers() {\n        // 监控导航性能\n        if (\"PerformanceObserver\" in window) {\n            const navObserver = new PerformanceObserver((list)=>{\n                for (const entry of list.getEntries()){\n                    if (entry.entryType === \"navigation\") {\n                        this.recordPageLoad(entry);\n                    }\n                }\n            });\n            navObserver.observe({\n                entryTypes: [\n                    \"navigation\"\n                ]\n            });\n            this.observers.push(navObserver);\n            // 监控资源加载\n            const resourceObserver = new PerformanceObserver((list)=>{\n                for (const entry of list.getEntries()){\n                    if (entry.entryType === \"resource\") {\n                        this.recordResourceLoad(entry);\n                    }\n                }\n            });\n            resourceObserver.observe({\n                entryTypes: [\n                    \"resource\"\n                ]\n            });\n            this.observers.push(resourceObserver);\n            // 监控用户交互\n            const interactionObserver = new PerformanceObserver((list)=>{\n                for (const entry of list.getEntries()){\n                    if (entry.entryType === \"event\") {\n                        this.recordInteraction(entry);\n                    }\n                }\n            });\n            try {\n                interactionObserver.observe({\n                    entryTypes: [\n                        \"event\"\n                    ]\n                });\n                this.observers.push(interactionObserver);\n            } catch (e) {\n                // Event timing API not supported\n                console.warn(\"Event timing API not supported\");\n            }\n        }\n    }\n    // 设置错误处理\n    setupErrorHandling() {\n        // JavaScript错误\n        window.addEventListener(\"error\", (event)=>{\n            this.recordError({\n                type: \"javascript\",\n                message: event.message,\n                stack: event.error?.stack,\n                timestamp: Date.now(),\n                route: window.location.pathname,\n                userAgent: navigator.userAgent\n            });\n        });\n        // Promise拒绝错误\n        window.addEventListener(\"unhandledrejection\", (event)=>{\n            this.recordError({\n                type: \"javascript\",\n                message: event.reason?.message || \"Unhandled Promise Rejection\",\n                stack: event.reason?.stack,\n                timestamp: Date.now(),\n                route: window.location.pathname,\n                userAgent: navigator.userAgent\n            });\n        });\n    }\n    // 记录页面加载性能\n    recordPageLoad(entry) {\n        const metric = {\n            startTime: entry.startTime,\n            endTime: entry.loadEventEnd,\n            duration: entry.loadEventEnd - entry.startTime,\n            route: window.location.pathname\n        };\n        this.addMetric({\n            pageLoad: metric\n        });\n        // 更新应用状态\n        _stores_appStore__WEBPACK_IMPORTED_MODULE_0__.useAppStore.getState().recordPageLoadTime(metric.duration);\n    }\n    // 记录资源加载性能\n    recordResourceLoad(entry) {\n        // 只记录API调用\n        if (entry.name.includes(\"/api/\")) {\n            const metric = {\n                endpoint: entry.name,\n                method: \"GET\",\n                startTime: entry.startTime,\n                endTime: entry.responseEnd,\n                duration: entry.responseEnd - entry.startTime,\n                status: 200,\n                size: entry.transferSize\n            };\n            this.addMetric({\n                network: metric\n            });\n        }\n    }\n    // 记录用户交互\n    recordInteraction(entry) {\n        const metric = {\n            type: entry.name,\n            element: entry.target?.tagName || \"unknown\",\n            timestamp: entry.startTime,\n            duration: entry.duration\n        };\n        this.addMetric({\n            interaction: metric\n        });\n    }\n    // 记录错误\n    recordError(error) {\n        this.addMetric({\n            error\n        });\n        _stores_appStore__WEBPACK_IMPORTED_MODULE_0__.useAppStore.getState().incrementErrorCount();\n    }\n    // 添加指标\n    addMetric(metric) {\n        if (!this.isEnabled) return;\n        this.metrics.push(metric);\n        // 限制指标数量，避免内存泄漏\n        if (this.metrics.length > 1000) {\n            this.metrics = this.metrics.slice(-500);\n        }\n    }\n    // 公共API：记录API调用\n    recordApiCall(endpoint, method, startTime, endTime, status, size) {\n        const metric = {\n            endpoint,\n            method,\n            startTime,\n            endTime,\n            duration: endTime - startTime,\n            status,\n            size\n        };\n        this.addMetric({\n            network: metric\n        });\n        _stores_appStore__WEBPACK_IMPORTED_MODULE_0__.useAppStore.getState().recordApiResponseTime(endpoint, metric.duration);\n    }\n    // 公共API：记录自定义交互\n    recordCustomInteraction(type, element, duration) {\n        const metric = {\n            type,\n            element,\n            timestamp: Date.now(),\n            duration\n        };\n        this.addMetric({\n            interaction: metric\n        });\n    }\n    // 获取性能报告\n    getPerformanceReport() {\n        const now = Date.now();\n        const last24Hours = now - 24 * 60 * 60 * 1000;\n        const recentMetrics = this.metrics.filter((metric)=>{\n            const timestamp = metric.pageLoad?.startTime || metric.network?.startTime || metric.interaction?.timestamp || metric.error?.timestamp || 0;\n            return timestamp > last24Hours;\n        });\n        return {\n            totalMetrics: recentMetrics.length,\n            pageLoads: recentMetrics.filter((m)=>m.pageLoad).length,\n            apiCalls: recentMetrics.filter((m)=>m.network).length,\n            interactions: recentMetrics.filter((m)=>m.interaction).length,\n            errors: recentMetrics.filter((m)=>m.error).length,\n            averagePageLoadTime: this.calculateAveragePageLoadTime(recentMetrics),\n            averageApiResponseTime: this.calculateAverageApiResponseTime(recentMetrics),\n            errorRate: this.calculateErrorRate(recentMetrics)\n        };\n    }\n    // 计算平均页面加载时间\n    calculateAveragePageLoadTime(metrics) {\n        const pageLoads = metrics.filter((m)=>m.pageLoad);\n        if (pageLoads.length === 0) return 0;\n        const total = pageLoads.reduce((sum, m)=>sum + (m.pageLoad?.duration || 0), 0);\n        return total / pageLoads.length;\n    }\n    // 计算平均API响应时间\n    calculateAverageApiResponseTime(metrics) {\n        const apiCalls = metrics.filter((m)=>m.network);\n        if (apiCalls.length === 0) return 0;\n        const total = apiCalls.reduce((sum, m)=>sum + (m.network?.duration || 0), 0);\n        return total / apiCalls.length;\n    }\n    // 计算错误率\n    calculateErrorRate(metrics) {\n        const errors = metrics.filter((m)=>m.error).length;\n        const total = metrics.length;\n        return total > 0 ? errors / total * 100 : 0;\n    }\n    // 启用/禁用监控\n    setEnabled(enabled) {\n        this.isEnabled = enabled;\n    }\n    // 清理资源\n    cleanup() {\n        this.observers.forEach((observer)=>observer.disconnect());\n        this.observers = [];\n        this.metrics = [];\n    }\n}\n// 创建全局实例\nconst performanceMonitor = new PerformanceMonitor();\n// React Hook\nconst usePerformanceMonitor = ()=>{\n    return {\n        recordApiCall: performanceMonitor.recordApiCall.bind(performanceMonitor),\n        recordInteraction: performanceMonitor.recordCustomInteraction.bind(performanceMonitor),\n        getReport: performanceMonitor.getPerformanceReport.bind(performanceMonitor),\n        setEnabled: performanceMonitor.setEnabled.bind(performanceMonitor)\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/performance/monitor.ts\n");

/***/ }),

/***/ "(ssr)/./lib/stores/appStore.ts":
/*!********************************!*\
  !*** ./lib/stores/appStore.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppData: () => (/* binding */ useAppData),\n/* harmony export */   useAppPerformance: () => (/* binding */ useAppPerformance),\n/* harmony export */   useAppPreferences: () => (/* binding */ useAppPreferences),\n/* harmony export */   useAppStore: () => (/* binding */ useAppStore),\n/* harmony export */   useAppUI: () => (/* binding */ useAppUI),\n/* harmony export */   useError: () => (/* binding */ useError),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage),\n/* harmony export */   useLoading: () => (/* binding */ useLoading),\n/* harmony export */   useTheme: () => (/* binding */ useTheme),\n/* harmony export */   useToasts: () => (/* binding */ useToasts)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var zustand_middleware_immer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zustand/middleware/immer */ \"(ssr)/./node_modules/zustand/esm/middleware/immer.mjs\");\n/* __next_internal_client_entry_do_not_use__ useAppStore,useAppPreferences,useAppUI,useAppData,useAppPerformance,useTheme,useLanguage,useLoading,useError,useToasts auto */ \n\n\n// 默认偏好设置\nconst defaultPreferences = {\n    theme: \"system\",\n    language: \"zh\",\n    fontSize: \"medium\",\n    animations: true,\n    notifications: {\n        browser: true,\n        email: false,\n        sms: false\n    },\n    privacy: {\n        analytics: true,\n        cookies: true,\n        dataSharing: false\n    },\n    accessibility: {\n        highContrast: false,\n        reducedMotion: false,\n        screenReader: false\n    }\n};\n// 默认应用状态\nconst defaultState = {\n    preferences: defaultPreferences,\n    ui: {\n        sidebarOpen: false,\n        loading: false,\n        error: null,\n        modal: {\n            isOpen: false,\n            type: null,\n            data: null\n        },\n        toast: []\n    },\n    data: {\n        lastSync: null,\n        version: \"1.0.0\",\n        buildTime: new Date().toISOString()\n    },\n    performance: {\n        pageLoadTime: 0,\n        apiResponseTimes: {},\n        errorCount: 0\n    }\n};\n// 生成唯一ID\nconst generateId = ()=>`${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n// 创建应用Store\nconst useAppStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((0,zustand_middleware_immer__WEBPACK_IMPORTED_MODULE_2__.immer)((set, get)=>({\n        ...defaultState,\n        // 偏好设置Actions\n        updatePreferences: (newPreferences)=>{\n            set((state)=>{\n                Object.assign(state.preferences, newPreferences);\n            });\n        },\n        resetPreferences: ()=>{\n            set((state)=>{\n                state.preferences = defaultPreferences;\n            });\n        },\n        // UI控制Actions\n        setSidebarOpen: (open)=>{\n            set((state)=>{\n                state.ui.sidebarOpen = open;\n            });\n        },\n        toggleSidebar: ()=>{\n            set((state)=>{\n                state.ui.sidebarOpen = !state.ui.sidebarOpen;\n            });\n        },\n        setLoading: (loading)=>{\n            set((state)=>{\n                state.ui.loading = loading;\n            });\n        },\n        setError: (error)=>{\n            set((state)=>{\n                state.ui.error = error;\n            });\n        },\n        // 模态框控制Actions\n        openModal: (type, data = null)=>{\n            set((state)=>{\n                state.ui.modal = {\n                    isOpen: true,\n                    type,\n                    data\n                };\n            });\n        },\n        closeModal: ()=>{\n            set((state)=>{\n                state.ui.modal = {\n                    isOpen: false,\n                    type: null,\n                    data: null\n                };\n            });\n        },\n        // Toast通知Actions\n        addToast: (toast)=>{\n            const id = generateId();\n            set((state)=>{\n                state.ui.toast.push({\n                    ...toast,\n                    id\n                });\n            });\n            // 自动移除Toast\n            if (toast.duration !== 0) {\n                setTimeout(()=>{\n                    get().removeToast(id);\n                }, toast.duration || 5000);\n            }\n            return id;\n        },\n        removeToast: (id)=>{\n            set((state)=>{\n                state.ui.toast = state.ui.toast.filter((t)=>t.id !== id);\n            });\n        },\n        clearToasts: ()=>{\n            set((state)=>{\n                state.ui.toast = [];\n            });\n        },\n        // 数据同步Actions\n        updateLastSync: ()=>{\n            set((state)=>{\n                state.data.lastSync = new Date().toISOString();\n            });\n        },\n        // 性能监控Actions\n        recordPageLoadTime: (time)=>{\n            set((state)=>{\n                state.performance.pageLoadTime = time;\n            });\n        },\n        recordApiResponseTime: (endpoint, time)=>{\n            set((state)=>{\n                state.performance.apiResponseTimes[endpoint] = time;\n            });\n        },\n        incrementErrorCount: ()=>{\n            set((state)=>{\n                state.performance.errorCount += 1;\n            });\n        },\n        resetPerformanceMetrics: ()=>{\n            set((state)=>{\n                state.performance = {\n                    pageLoadTime: 0,\n                    apiResponseTimes: {},\n                    errorCount: 0\n                };\n            });\n        }\n    })), {\n    name: \"periodhub-app-store\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.createJSONStorage)(()=>localStorage),\n    partialize: (state)=>({\n            preferences: state.preferences,\n            data: {\n                lastSync: state.data.lastSync,\n                version: state.data.version\n            }\n        })\n}), {\n    name: \"PeriodHub App Store\"\n}));\n// 选择器Hooks\nconst useAppPreferences = ()=>useAppStore((state)=>state.preferences);\nconst useAppUI = ()=>useAppStore((state)=>state.ui);\nconst useAppData = ()=>useAppStore((state)=>state.data);\nconst useAppPerformance = ()=>useAppStore((state)=>state.performance);\n// 便捷Hooks\nconst useTheme = ()=>useAppStore((state)=>state.preferences.theme);\nconst useLanguage = ()=>useAppStore((state)=>state.preferences.language);\nconst useLoading = ()=>useAppStore((state)=>state.ui.loading);\nconst useError = ()=>useAppStore((state)=>state.ui.error);\nconst useToasts = ()=>useAppStore((state)=>state.ui.toast);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/stores/appStore.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7d6af9bbd4ce\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZXJpb2RodWItaGVhbHRoLy4vYXBwL2dsb2JhbHMuY3NzPzg2OWQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3ZDZhZjliYmQ0Y2VcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_advanced_AppProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/advanced/AppProvider */ \"(rsc)/./components/advanced/AppProvider.tsx\");\n\n\n\n\n\n// Generate metadata\nconst metadata = {\n    title: {\n        template: \"%s | periodhub.health\",\n        default: \"periodhub.health - Your Guide to Menstrual Wellness\"\n    },\n    description: \"Your compassionate guide to navigating menstrual pain with effective solutions, supportive resources, and a path to better menstrual health.\",\n    keywords: [\n        \"period pain relief\",\n        \"menstrual cramps\",\n        \"natural remedies\",\n        \"period management\",\n        \"women's health\",\n        \"dysmenorrhea treatment\",\n        \"periodhub.health\",\n        \"menstrual health\",\n        \"articles\",\n        \"therapies\"\n    ],\n    authors: [\n        {\n            name: \"periodhub.health team\"\n        }\n    ],\n    creator: \"periodhub.health\",\n    publisher: \"periodhub.health\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || \"https://periodhub.health\"),\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    // Get the current locale from headers or default to 'en'\n    const headersList = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n    const pathname = headersList.get(\"x-pathname\") || \"\";\n    const locale = pathname.startsWith(\"/zh\") ? \"zh\" : \"en\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        className: `scroll-smooth ${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#ffffff\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=5\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://v3.fal.media\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://unpkg.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased bg-neutral-50 text-neutral-900 flex flex-col min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_advanced_AppProvider__WEBPACK_IMPORTED_MODULE_3__.AppProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sr-only\",\n                        children: \"This website provides information about menstrual health for educational purposes only. The content is not intended to be a substitute for professional medical advice, diagnosis, or treatment. Always seek the advice of your physician or other qualified health provider with any questions you may have.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBUU1BO0FBTmlDO0FBRWhCO0FBQzBDO0FBU2pFLG9CQUFvQjtBQUNiLE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO1FBQ0xDLFVBQVU7UUFDVkMsU0FBUztJQUNYO0lBQ0FDLGFBQWE7SUFDYkMsVUFBVTtRQUNSO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFDREMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBd0I7S0FBRTtJQUM1Q0MsU0FBUztJQUNUQyxXQUFXO0lBQ1hDLGlCQUFpQjtRQUNmQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsV0FBVztJQUNiO0lBQ0FDLGNBQWMsSUFBSUMsSUFBSUMsUUFBUUMsR0FBRyxDQUFDQyxvQkFBb0IsSUFBSTtJQUMxREMsUUFBUTtRQUNOQyxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsV0FBVztZQUNURixPQUFPO1lBQ1BDLFFBQVE7WUFDUixxQkFBcUIsQ0FBQztZQUN0QixxQkFBcUI7WUFDckIsZUFBZSxDQUFDO1FBQ2xCO0lBQ0Y7QUFDRixFQUFFO0FBRWEsU0FBU0UsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MseURBQXlEO0lBQ3pELE1BQU1DLGNBQWMzQixxREFBT0E7SUFDM0IsTUFBTTRCLFdBQVdELFlBQVlFLEdBQUcsQ0FBQyxpQkFBaUI7SUFDbEQsTUFBTUMsU0FBU0YsU0FBU0csVUFBVSxDQUFDLFNBQVMsT0FBTztJQUVuRCxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBTUg7UUFBUUksV0FBVyxDQUFDLGNBQWMsRUFBRW5DLDJMQUFjLENBQUMsQ0FBQzs7MEJBQzlELDhEQUFDcUM7O2tDQUNDLDhEQUFDQzt3QkFBSzVCLE1BQUs7d0JBQWM2QixTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDRDt3QkFBSzVCLE1BQUs7d0JBQVc2QixTQUFROzs7Ozs7a0NBQzlCLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBYUMsTUFBSzs7Ozs7O2tDQUM1Qiw4REFBQ0Y7d0JBQUtDLEtBQUk7d0JBQWFDLE1BQUs7d0JBQTRCQyxhQUFZOzs7Ozs7a0NBR3BFLDhEQUFDSDt3QkFBS0MsS0FBSTt3QkFBZUMsTUFBSzs7Ozs7O2tDQUM5Qiw4REFBQ0Y7d0JBQUtDLEtBQUk7d0JBQWFDLE1BQUs7Ozs7OztrQ0FHNUIsOERBQUNGO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLO3dCQUFlRSxPQUFNOzs7Ozs7a0NBQzNDLDhEQUFDSjt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzt3QkFBWUcsTUFBSzs7Ozs7O2tDQUN2Qyw4REFBQ0w7d0JBQUtDLEtBQUk7d0JBQW1CQyxNQUFLOzs7Ozs7a0NBQ2xDLDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBV0MsTUFBSzs7Ozs7Ozs7Ozs7OzBCQUU1Qiw4REFBQ0k7Z0JBQUtYLFdBQVU7O2tDQUNkLDhEQUFDakMseUVBQVdBO2tDQUNUeUI7Ozs7OztrQ0FJSCw4REFBQ29CO3dCQUFJWixXQUFVO2tDQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRakMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wZXJpb2RodWItaGVhbHRoLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnO1xuaW1wb3J0IHsgaGVhZGVycyB9IGZyb20gJ25leHQvaGVhZGVycyc7XG5cbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgeyBBcHBQcm92aWRlciB9IGZyb20gJy4uL2NvbXBvbmVudHMvYWR2YW5jZWQvQXBwUHJvdmlkZXInO1xuXG4vLyBGb250IGNvbmZpZ3VyYXRpb25cbmNvbnN0IGludGVyID0gSW50ZXIoe1xuICBzdWJzZXRzOiBbJ2xhdGluJ10sXG4gIGRpc3BsYXk6ICdzd2FwJyxcbiAgdmFyaWFibGU6ICctLWZvbnQtaW50ZXInLFxufSk7XG5cbi8vIEdlbmVyYXRlIG1ldGFkYXRhXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZToge1xuICAgIHRlbXBsYXRlOiAnJXMgfCBwZXJpb2RodWIuaGVhbHRoJyxcbiAgICBkZWZhdWx0OiAncGVyaW9kaHViLmhlYWx0aCAtIFlvdXIgR3VpZGUgdG8gTWVuc3RydWFsIFdlbGxuZXNzJyxcbiAgfSxcbiAgZGVzY3JpcHRpb246ICdZb3VyIGNvbXBhc3Npb25hdGUgZ3VpZGUgdG8gbmF2aWdhdGluZyBtZW5zdHJ1YWwgcGFpbiB3aXRoIGVmZmVjdGl2ZSBzb2x1dGlvbnMsIHN1cHBvcnRpdmUgcmVzb3VyY2VzLCBhbmQgYSBwYXRoIHRvIGJldHRlciBtZW5zdHJ1YWwgaGVhbHRoLicsXG4gIGtleXdvcmRzOiBbXG4gICAgJ3BlcmlvZCBwYWluIHJlbGllZicsXG4gICAgJ21lbnN0cnVhbCBjcmFtcHMnLFxuICAgICduYXR1cmFsIHJlbWVkaWVzJyxcbiAgICAncGVyaW9kIG1hbmFnZW1lbnQnLFxuICAgICd3b21lblxcJ3MgaGVhbHRoJyxcbiAgICAnZHlzbWVub3JyaGVhIHRyZWF0bWVudCcsXG4gICAgJ3BlcmlvZGh1Yi5oZWFsdGgnLFxuICAgICdtZW5zdHJ1YWwgaGVhbHRoJyxcbiAgICAnYXJ0aWNsZXMnLFxuICAgICd0aGVyYXBpZXMnXG4gIF0sXG4gIGF1dGhvcnM6IFt7IG5hbWU6ICdwZXJpb2RodWIuaGVhbHRoIHRlYW0nIH1dLFxuICBjcmVhdG9yOiAncGVyaW9kaHViLmhlYWx0aCcsXG4gIHB1Ymxpc2hlcjogJ3BlcmlvZGh1Yi5oZWFsdGgnLFxuICBmb3JtYXREZXRlY3Rpb246IHtcbiAgICBlbWFpbDogZmFsc2UsXG4gICAgYWRkcmVzczogZmFsc2UsXG4gICAgdGVsZXBob25lOiBmYWxzZSxcbiAgfSxcbiAgbWV0YWRhdGFCYXNlOiBuZXcgVVJMKHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0JBU0VfVVJMIHx8ICdodHRwczovL3BlcmlvZGh1Yi5oZWFsdGgnKSxcbiAgcm9ib3RzOiB7XG4gICAgaW5kZXg6IHRydWUsXG4gICAgZm9sbG93OiB0cnVlLFxuICAgIGdvb2dsZUJvdDoge1xuICAgICAgaW5kZXg6IHRydWUsXG4gICAgICBmb2xsb3c6IHRydWUsXG4gICAgICAnbWF4LXZpZGVvLXByZXZpZXcnOiAtMSxcbiAgICAgICdtYXgtaW1hZ2UtcHJldmlldyc6ICdsYXJnZScsXG4gICAgICAnbWF4LXNuaXBwZXQnOiAtMSxcbiAgICB9LFxuICB9LFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICAvLyBHZXQgdGhlIGN1cnJlbnQgbG9jYWxlIGZyb20gaGVhZGVycyBvciBkZWZhdWx0IHRvICdlbidcbiAgY29uc3QgaGVhZGVyc0xpc3QgPSBoZWFkZXJzKCk7XG4gIGNvbnN0IHBhdGhuYW1lID0gaGVhZGVyc0xpc3QuZ2V0KCd4LXBhdGhuYW1lJykgfHwgJyc7XG4gIGNvbnN0IGxvY2FsZSA9IHBhdGhuYW1lLnN0YXJ0c1dpdGgoJy96aCcpID8gJ3poJyA6ICdlbic7XG5cbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPXtsb2NhbGV9IGNsYXNzTmFtZT17YHNjcm9sbC1zbW9vdGggJHtpbnRlci52YXJpYWJsZX1gfT5cbiAgICAgIDxoZWFkPlxuICAgICAgICA8bWV0YSBuYW1lPVwidGhlbWUtY29sb3JcIiBjb250ZW50PVwiI2ZmZmZmZlwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MSwgbWF4aW11bS1zY2FsZT01XCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwicHJlY29ubmVjdFwiIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwicHJlY29ubmVjdFwiIGhyZWY9XCJodHRwczovL2ZvbnRzLmdzdGF0aWMuY29tXCIgY3Jvc3NPcmlnaW49XCJhbm9ueW1vdXNcIiAvPlxuXG4gICAgICAgIHsvKiBQZXJmb3JtYW5jZSBvcHRpbWl6YXRpb25zICovfVxuICAgICAgICA8bGluayByZWw9XCJkbnMtcHJlZmV0Y2hcIiBocmVmPVwiaHR0cHM6Ly92My5mYWwubWVkaWFcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJwcmVjb25uZWN0XCIgaHJlZj1cImh0dHBzOi8vdW5wa2cuY29tXCIgLz5cblxuICAgICAgICB7LyogRmF2aWNvbiBhbmQgYXBwIGljb25zICovfVxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIHNpemVzPVwiYW55XCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvaWNvbi5zdmdcIiB0eXBlPVwiaW1hZ2Uvc3ZnK3htbFwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImFwcGxlLXRvdWNoLWljb25cIiBocmVmPVwiL2FwcGxlLXRvdWNoLWljb24ucG5nXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwibWFuaWZlc3RcIiBocmVmPVwiL21hbmlmZXN0Lmpzb25cIiAvPlxuICAgICAgPC9oZWFkPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiYW50aWFsaWFzZWQgYmctbmV1dHJhbC01MCB0ZXh0LW5ldXRyYWwtOTAwIGZsZXggZmxleC1jb2wgbWluLWgtc2NyZWVuXCI+XG4gICAgICAgIDxBcHBQcm92aWRlcj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvQXBwUHJvdmlkZXI+XG5cbiAgICAgICAgey8qIE1lZGljYWwgRGlzY2xhaW1lciAtIEZvciBjb21wbGlhbmNlIGFuZCB1c2VyIHNhZmV0eSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzci1vbmx5XCI+XG4gICAgICAgICAgVGhpcyB3ZWJzaXRlIHByb3ZpZGVzIGluZm9ybWF0aW9uIGFib3V0IG1lbnN0cnVhbCBoZWFsdGggZm9yIGVkdWNhdGlvbmFsIHB1cnBvc2VzIG9ubHkuXG4gICAgICAgICAgVGhlIGNvbnRlbnQgaXMgbm90IGludGVuZGVkIHRvIGJlIGEgc3Vic3RpdHV0ZSBmb3IgcHJvZmVzc2lvbmFsIG1lZGljYWwgYWR2aWNlLCBkaWFnbm9zaXMsIG9yIHRyZWF0bWVudC5cbiAgICAgICAgICBBbHdheXMgc2VlayB0aGUgYWR2aWNlIG9mIHlvdXIgcGh5c2ljaWFuIG9yIG90aGVyIHF1YWxpZmllZCBoZWFsdGggcHJvdmlkZXIgd2l0aCBhbnkgcXVlc3Rpb25zIHlvdSBtYXkgaGF2ZS5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwiaGVhZGVycyIsIkFwcFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsInRlbXBsYXRlIiwiZGVmYXVsdCIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJhdXRob3JzIiwibmFtZSIsImNyZWF0b3IiLCJwdWJsaXNoZXIiLCJmb3JtYXREZXRlY3Rpb24iLCJlbWFpbCIsImFkZHJlc3MiLCJ0ZWxlcGhvbmUiLCJtZXRhZGF0YUJhc2UiLCJVUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQkFTRV9VUkwiLCJyb2JvdHMiLCJpbmRleCIsImZvbGxvdyIsImdvb2dsZUJvdCIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImhlYWRlcnNMaXN0IiwicGF0aG5hbWUiLCJnZXQiLCJsb2NhbGUiLCJzdGFydHNXaXRoIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSIsImhlYWQiLCJtZXRhIiwiY29udGVudCIsImxpbmsiLCJyZWwiLCJocmVmIiwiY3Jvc3NPcmlnaW4iLCJzaXplcyIsInR5cGUiLCJib2R5IiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/advanced/AppProvider.tsx":
/*!*********************************************!*\
  !*** ./components/advanced/AppProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppProvider: () => (/* binding */ e0),
/* harmony export */   ComponentProvider: () => (/* binding */ e2),
/* harmony export */   PageProvider: () => (/* binding */ e1),
/* harmony export */   usePagePerformance: () => (/* binding */ e3),
/* harmony export */   useUserTracking: () => (/* binding */ e4)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#AppProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#PageProvider`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#ComponentProvider`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#usePagePerformance`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#useUserTracking`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/immer","vendor-chunks/lucide-react","vendor-chunks/zustand"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();