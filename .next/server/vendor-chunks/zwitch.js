"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zwitch";
exports.ids = ["vendor-chunks/zwitch"];
exports.modules = {

/***/ "(rsc)/./node_modules/zwitch/index.js":
/*!**************************************!*\
  !*** ./node_modules/zwitch/index.js ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   zwitch: () => (/* binding */ zwitch)\n/* harmony export */ });\n/**\n * @callback Handler\n *   Handle a value, with a certain ID field set to a certain value.\n *   The ID field is passed to `zwitch`, and it’s value is this function’s\n *   place on the `handlers` record.\n * @param {...any} parameters\n *   Arbitrary parameters passed to the zwitch.\n *   The first will be an object with a certain ID field set to a certain value.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback UnknownHandler\n *   Handle values that do have a certain ID field, but it’s set to a value\n *   that is not listed in the `handlers` record.\n * @param {unknown} value\n *   An object with a certain ID field set to an unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback InvalidHandler\n *   Handle values that do not have a certain ID field.\n * @param {unknown} value\n *   Any unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {void|null|undefined|never}\n *   This should crash or return nothing.\n */\n\n/**\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @typedef Options\n *   Configuration (required).\n * @property {Invalid} [invalid]\n *   Handler to use for invalid values.\n * @property {Unknown} [unknown]\n *   Handler to use for unknown values.\n * @property {Handlers} [handlers]\n *   Handlers to use.\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Handle values based on a field.\n *\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @param {string} key\n *   Field to switch on.\n * @param {Options<Invalid, Unknown, Handlers>} [options]\n *   Configuration (required).\n * @returns {{unknown: Unknown, invalid: Invalid, handlers: Handlers, (...parameters: Parameters<Handlers[keyof Handlers]>): ReturnType<Handlers[keyof Handlers]>, (...parameters: Parameters<Unknown>): ReturnType<Unknown>}}\n */\nfunction zwitch(key, options) {\n  const settings = options || {}\n\n  /**\n   * Handle one value.\n   *\n   * Based on the bound `key`, a respective handler will be called.\n   * If `value` is not an object, or doesn’t have a `key` property, the special\n   * “invalid” handler will be called.\n   * If `value` has an unknown `key`, the special “unknown” handler will be\n   * called.\n   *\n   * All arguments, and the context object, are passed through to the handler,\n   * and it’s result is returned.\n   *\n   * @this {unknown}\n   *   Any context object.\n   * @param {unknown} [value]\n   *   Any value.\n   * @param {...unknown} parameters\n   *   Arbitrary parameters passed to the zwitch.\n   * @property {Handler} invalid\n   *   Handle for values that do not have a certain ID field.\n   * @property {Handler} unknown\n   *   Handle values that do have a certain ID field, but it’s set to a value\n   *   that is not listed in the `handlers` record.\n   * @property {Handlers} handlers\n   *   Record of handlers.\n   * @returns {unknown}\n   *   Anything.\n   */\n  function one(value, ...parameters) {\n    /** @type {Handler|undefined} */\n    let fn = one.invalid\n    const handlers = one.handlers\n\n    if (value && own.call(value, key)) {\n      // @ts-expect-error Indexable.\n      const id = String(value[key])\n      // @ts-expect-error Indexable.\n      fn = own.call(handlers, id) ? handlers[id] : one.unknown\n    }\n\n    if (fn) {\n      return fn.call(this, value, ...parameters)\n    }\n  }\n\n  one.handlers = settings.handlers || {}\n  one.invalid = settings.invalid\n  one.unknown = settings.unknown\n\n  // @ts-expect-error: matches!\n  return one\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/zwitch/index.js\n");

/***/ })

};
;