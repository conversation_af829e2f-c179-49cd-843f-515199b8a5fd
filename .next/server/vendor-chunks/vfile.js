"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vfile";
exports.ids = ["vendor-chunks/vfile"];
exports.modules = {

/***/ "(rsc)/./node_modules/vfile/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/vfile/lib/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VFile: () => (/* binding */ VFile)\n/* harmony export */ });\n/* harmony import */ var vfile_message__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! vfile-message */ \"(rsc)/./node_modules/vfile-message/lib/index.js\");\n/* harmony import */ var _minpath__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! #minpath */ \"node:path\");\n/* harmony import */ var _minproc__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! #minproc */ \"node:process\");\n/* harmony import */ var _minurl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! #minurl */ \"(rsc)/./node_modules/vfile/lib/minurl.shared.js\");\n/* harmony import */ var _minurl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! #minurl */ \"node:url\");\n/**\n * @import {Node, Point, Position} from 'unist'\n * @import {Options as MessageOptions} from 'vfile-message'\n * @import {Compatible, Data, Map, Options, Value} from 'vfile'\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\n\n\n\n\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n */\nconst order = /** @type {const} */ ([\n  'history',\n  'path',\n  'basename',\n  'stem',\n  'extname',\n  'dirname'\n])\n\nclass VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Uint8Array` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options\n\n    if (!value) {\n      options = {}\n    } else if ((0,_minurl__WEBPACK_IMPORTED_MODULE_0__.isUrl)(value)) {\n      options = {path: value}\n    } else if (typeof value === 'string' || isUint8Array(value)) {\n      options = {value}\n    } else {\n      options = value\n    }\n\n    /* eslint-disable no-unused-expressions */\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    // Prevent calling `cwd` (which could be expensive) if it’s not needed;\n    // the empty string will be overridden in the next block.\n    this.cwd = 'cwd' in options ? '' : _minproc__WEBPACK_IMPORTED_MODULE_1__.cwd()\n\n    /**\n     * Place to store custom info (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {}\n\n    /**\n     * List of file paths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = []\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = []\n\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1\n\n    while (++index < order.length) {\n      const field = order[index]\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (\n        field in options &&\n        options[field] !== undefined &&\n        options[field] !== null\n      ) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[field] = field === 'history' ? [...options[field]] : options[field]\n      }\n    }\n\n    /** @type {string} */\n    let field\n\n    // Set non-path related properties.\n    for (field in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(field)) {\n        // @ts-expect-error: fine to set other things.\n        this[field] = options[field]\n      }\n    }\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   *\n   * @returns {string | undefined}\n   *   Basename.\n   */\n  get basename() {\n    return typeof this.path === 'string'\n      ? _minpath__WEBPACK_IMPORTED_MODULE_2__.basename(this.path)\n      : undefined\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} basename\n   *   Basename.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename')\n    assertPart(basename, 'basename')\n    this.path = _minpath__WEBPACK_IMPORTED_MODULE_2__.join(this.dirname || '', basename)\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   *\n   * @returns {string | undefined}\n   *   Dirname.\n   */\n  get dirname() {\n    return typeof this.path === 'string'\n      ? _minpath__WEBPACK_IMPORTED_MODULE_2__.dirname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} dirname\n   *   Dirname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname')\n    this.path = _minpath__WEBPACK_IMPORTED_MODULE_2__.join(dirname || '', this.basename)\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   *\n   * @returns {string | undefined}\n   *   Extname.\n   */\n  get extname() {\n    return typeof this.path === 'string'\n      ? _minpath__WEBPACK_IMPORTED_MODULE_2__.extname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} extname\n   *   Extname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname')\n    assertPath(this.dirname, 'extname')\n\n    if (extname) {\n      if (extname.codePointAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`')\n      }\n\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots')\n      }\n    }\n\n    this.path = _minpath__WEBPACK_IMPORTED_MODULE_2__.join(this.dirname, this.stem + (extname || ''))\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   *   Path.\n   */\n  get path() {\n    return this.history[this.history.length - 1]\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {URL | string} path\n   *   Path.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set path(path) {\n    if ((0,_minurl__WEBPACK_IMPORTED_MODULE_0__.isUrl)(path)) {\n      path = (0,_minurl__WEBPACK_IMPORTED_MODULE_3__.fileURLToPath)(path)\n    }\n\n    assertNonEmpty(path, 'path')\n\n    if (this.path !== path) {\n      this.history.push(path)\n    }\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * @returns {string | undefined}\n   *   Stem.\n   */\n  get stem() {\n    return typeof this.path === 'string'\n      ? _minpath__WEBPACK_IMPORTED_MODULE_2__.basename(this.path, this.extname)\n      : undefined\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} stem\n   *   Stem.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem')\n    assertPart(stem, 'stem')\n    this.path = _minpath__WEBPACK_IMPORTED_MODULE_2__.join(this.dirname || '', stem + (this.extname || ''))\n  }\n\n  // Normal prototypal methods.\n  /**\n   * Create a fatal message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `true` (error; file not usable)\n   * and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Never.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = true\n\n    throw message\n  }\n\n  /**\n   * Create an info message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `undefined` (info; change\n   * likely not needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = undefined\n\n    return message\n  }\n\n  /**\n   * Create a message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `false` (warning; change may be\n   * needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(causeOrReason, optionsOrParentOrPlace, origin) {\n    const message = new vfile_message__WEBPACK_IMPORTED_MODULE_4__.VFileMessage(\n      // @ts-expect-error: the overloads are fine.\n      causeOrReason,\n      optionsOrParentOrPlace,\n      origin\n    )\n\n    if (this.path) {\n      message.name = this.path + ':' + message.name\n      message.file = this.path\n    }\n\n    message.fatal = false\n\n    this.messages.push(message)\n\n    return message\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * > **Note**: which encodings are supported depends on the engine.\n   * > For info on Node.js, see:\n   * > <https://nodejs.org/api/util.html#whatwg-supported-encodings>.\n   *\n   * @param {string | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Uint8Array`\n   *   (default: `'utf-8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    if (this.value === undefined) {\n      return ''\n    }\n\n    if (typeof this.value === 'string') {\n      return this.value\n    }\n\n    const decoder = new TextDecoder(encoding || undefined)\n    return decoder.decode(this.value)\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {undefined}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(_minpath__WEBPACK_IMPORTED_MODULE_2__.sep)) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + _minpath__WEBPACK_IMPORTED_MODULE_2__.sep + '`'\n    )\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/vfile/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/vfile/lib/minurl.shared.js":
/*!*************************************************!*\
  !*** ./node_modules/vfile/lib/minurl.shared.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isUrl: () => (/* binding */ isUrl)\n/* harmony export */ });\n/**\n * Checks if a value has the shape of a WHATWG URL object.\n *\n * Using a symbol or instanceof would not be able to recognize URL objects\n * coming from other implementations (e.g. in Electron), so instead we are\n * checking some well known properties for a lack of a better test.\n *\n * We use `href` and `protocol` as they are the only properties that are\n * easy to retrieve and calculate due to the lazy nature of the getters.\n *\n * We check for auth attribute to distinguish legacy url instance with\n * WHATWG URL instance.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */\n// From: <https://github.com/nodejs/node/blob/6a3403c/lib/internal/url.js#L720>\nfunction isUrl(fileUrlOrPath) {\n  return Boolean(\n    fileUrlOrPath !== null &&\n      typeof fileUrlOrPath === 'object' &&\n      'href' in fileUrlOrPath &&\n      fileUrlOrPath.href &&\n      'protocol' in fileUrlOrPath &&\n      fileUrlOrPath.protocol &&\n      // @ts-expect-error: indexing is fine.\n      fileUrlOrPath.auth === undefined\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/vfile/lib/minurl.shared.js\n");

/***/ })

};
;